@echo off
chcp 65001 >nul
title Connection Error Fix Test

echo.
echo =========================================================
echo   Connection Error Fix - Sports Data API Test
echo =========================================================
echo.

echo Step 1: Get browser authentication info
echo.
echo First, let's get the authentication data from your browser:
echo.
java -cp target/classes com.zqsport.http.BrowserAuthHelper
echo.

echo =========================================================
echo Step 2: Test advanced HTTP client
echo =========================================================
echo.
echo Testing with advanced HTTP client (bypasses SSL, simulates browser)...
echo.
java "-Dfile.encoding=UTF-8" -cp "target/classes;target/dependency/*" com.zqsport.http.AdvancedHttpClient
echo.

echo =========================================================
echo Step 3: Interactive authentication test
echo =========================================================
echo.
echo Now let's test with your browser authentication data:
echo.
java "-Dfile.encoding=UTF-8" -cp "target/classes;target/dependency/*" com.zqsport.http.AuthenticatedTest
echo.

echo =========================================================
echo Test completed!
echo.
echo If connection still fails:
echo 1. Copy the working browser request headers
echo 2. Try using a VPN or proxy
echo 3. Consider browser automation approach
echo.
echo Press any key to exit...
pause >nul
