# 控制台中文乱码问题解决方案

## 🔍 问题原因

Windows控制台中文乱码是由于以下原因造成的：

1. **默认代码页** - Windows控制台默认使用GBK编码(936)，而Java程序使用UTF-8
2. **PowerShell编码限制** - PowerShell对某些Unicode字符支持有限
3. **字体限制** - 控制台字体可能不支持某些Unicode字符

## 🛠️ 解决方案

### 方案1: 使用命令提示符 (推荐)

```batch
# 1. 打开命令提示符 (不是PowerShell)
# 2. 设置UTF-8编码
chcp 65001

# 3. 运行程序
java "-Dfile.encoding=UTF-8" "-Dconsole.encoding=UTF-8" -cp "target/classes;target/dependency/*" com.zqsport.SportsDataHttpApp
```

### 方案2: 使用提供的批处理文件

我已经为您创建了以下批处理文件：

1. **run-cmd.bat** - 使用cmd运行编码测试
2. **run-sports-app.bat** - 启动主程序
3. **run-network-test.bat** - 网络连接测试

**使用方法：双击批处理文件即可**

### 方案3: 修改控制台字体 (可选)

1. 右键点击控制台标题栏 → 属性
2. 在"字体"选项卡中选择支持Unicode的字体，如：
   - Consolas
   - Lucida Console  
   - MS Gothic

### 方案4: 使用IDE运行 (开发推荐)

在IDE（如IntelliJ IDEA、Eclipse）中运行程序，IDE通常能正确处理UTF-8编码。

## 🚀 快速测试

### 测试1: 基础编码测试

```batch
run-cmd.bat
```

### 测试2: 网络连接测试

```batch
run-network-test.bat  
```

### 测试3: 完整程序运行

```batch
run-sports-app.bat
```

## 📋 编码状态检查

程序会显示当前编码信息：

```
=== 编码信息检查 ===
Default Charset: UTF-8
File Encoding: UTF-8  
Console Encoding: UTF-8
OS Name: Windows 11
```

如果显示为UTF-8，说明编码配置正确。

## 🔧 高级解决方案

### 1. 永久修改系统编码 (Windows 10/11)

```
控制面板 → 区域 → 管理 → 更改系统区域设置 → 勾选"Beta:使用Unicode UTF-8提供全球语言支持"
```

**注意：需要重启系统**

### 2. 使用环境变量

```batch
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8
java -cp "target/classes;target/dependency/*" com.zqsport.SportsDataHttpApp
```

### 3. 修改Java程序使用英文输出

如果中文乱码问题持续，我可以修改程序使用英文输出：

```java
// 原来：System.out.println("🌐 网络连接测试");
// 修改为：System.out.println("🌐 Network Connection Test");
```

## 🎯 推荐操作流程

1. **首先尝试** `run-cmd.bat` 测试编码
2. **如果正常** 使用 `run-sports-app.bat` 启动主程序  
3. **如果仍有问题** 使用IDE运行或考虑英文版本

## 💡 其他建议

### 开发环境
- 使用IDE（如IntelliJ IDEA）进行开发和测试
- IDE通常能正确处理编码问题

### 生产环境  
- 部署到Linux服务器，UTF-8编码更稳定
- 使用远程连接工具（如SSH客户端）

### 临时方案
- 程序主要功能（网络连接、数据获取）不受编码影响
- 只是显示效果问题，不影响程序逻辑

---

**总结**: 通过设置正确的代码页和JVM编码参数，大部分中文乱码问题都能得到解决。如果问题持续，建议使用IDE运行或英文版本。
