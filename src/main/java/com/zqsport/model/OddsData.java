package com.zqsport.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 赔率数据模型
 */
public class OddsData {
    
    private String eventId;
    private String eventName;
    private String homeTeam;
    private String awayTeam;
    private LocalDateTime startTime;
    private String league;
    private Map<String, Double> odds; // 赔率数据，例如 "1X2_1" -> 2.50
    private String status;
    private Integer score1; // 主队得分
    private Integer score2; // 客队得分
    
    // 构造函数
    public OddsData() {}
    
    public OddsData(String eventId, String eventName) {
        this.eventId = eventId;
        this.eventName = eventName;
    }
    
    // Getter和Setter方法
    public String getEventId() { return eventId; }
    public void setEventId(String eventId) { this.eventId = eventId; }
    
    public String getEventName() { return eventName; }
    public void setEventName(String eventName) { this.eventName = eventName; }
    
    public String getHomeTeam() { return homeTeam; }
    public void setHomeTeam(String homeTeam) { this.homeTeam = homeTeam; }
    
    public String getAwayTeam() { return awayTeam; }
    public void setAwayTeam(String awayTeam) { this.awayTeam = awayTeam; }
    
    public LocalDateTime getStartTime() { return startTime; }
    public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
    
    public String getLeague() { return league; }
    public void setLeague(String league) { this.league = league; }
    
    public Map<String, Double> getOdds() { return odds; }
    public void setOdds(Map<String, Double> odds) { this.odds = odds; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public Integer getScore1() { return score1; }
    public void setScore1(Integer score1) { this.score1 = score1; }
    
    public Integer getScore2() { return score2; }
    public void setScore2(Integer score2) { this.score2 = score2; }
    
    /**
     * 获取格式化的开始时间
     */
    public String getFormattedStartTime() {
        if (startTime == null) return "未知";
        return startTime.format(DateTimeFormatter.ofPattern("MM-dd HH:mm"));
    }
    
    /**
     * 获取比赛对阵信息
     */
    public String getMatchup() {
        if (homeTeam != null && awayTeam != null) {
            return homeTeam + " vs " + awayTeam;
        }
        return eventName != null ? eventName : "未知对阵";
    }
    
    /**
     * 获取比分信息
     */
    public String getScoreInfo() {
        if (score1 != null && score2 != null) {
            return score1 + " - " + score2;
        }
        return "未开始";
    }
    
    /**
     * 获取主要赔率信息（1X2）
     */
    public String getMainOdds() {
        if (odds == null || odds.isEmpty()) {
            return "无赔率";
        }
        
        StringBuilder sb = new StringBuilder();
        // 查找1X2赔率
        Double homeWin = odds.get("1");
        Double draw = odds.get("X");
        Double awayWin = odds.get("2");
        
        if (homeWin != null || draw != null || awayWin != null) {
            sb.append("1X2: ");
            sb.append(homeWin != null ? String.format("%.2f", homeWin) : "-");
            sb.append(" / ");
            sb.append(draw != null ? String.format("%.2f", draw) : "-");
            sb.append(" / ");
            sb.append(awayWin != null ? String.format("%.2f", awayWin) : "-");
        } else {
            // 如果没有1X2，显示其他可用赔率
            sb.append("赔率: ");
            int count = 0;
            for (Map.Entry<String, Double> entry : odds.entrySet()) {
                if (count > 0) sb.append(", ");
                sb.append(entry.getKey()).append(":").append(String.format("%.2f", entry.getValue()));
                count++;
                if (count >= 3) break; // 最多显示3个赔率
            }
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s | %s | %s | %s", 
                eventId, 
                getMatchup(), 
                getFormattedStartTime(), 
                getScoreInfo(), 
                getMainOdds());
    }
    
    /**
     * 获取详细信息字符串
     */
    public String toDetailedString() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 赛事详情 ===\n");
        sb.append("ID: ").append(eventId).append("\n");
        sb.append("对阵: ").append(getMatchup()).append("\n");
        sb.append("联赛: ").append(league != null ? league : "未知").append("\n");
        sb.append("时间: ").append(getFormattedStartTime()).append("\n");
        sb.append("状态: ").append(status != null ? status : "未知").append("\n");
        sb.append("比分: ").append(getScoreInfo()).append("\n");
        
        if (odds != null && !odds.isEmpty()) {
            sb.append("赔率:\n");
            for (Map.Entry<String, Double> entry : odds.entrySet()) {
                sb.append("  ").append(entry.getKey()).append(": ").append(String.format("%.2f", entry.getValue())).append("\n");
            }
        } else {
            sb.append("赔率: 无数据\n");
        }
        
        return sb.toString();
    }
}
