package com.zqsport.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 时间格式化工具类
 * 用于统一处理各种时间格式的转换和显示
 */
public class TimeFormatter {
    
    // 常用的时间格式
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("MM-dd HH:mm");
    private static final DateTimeFormatter FULL_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter TIME_ONLY_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    
    /**
     * 将毫秒时间戳转换为标准日期时间格式
     * @param timestampMs 毫秒时间戳
     * @return 格式化后的时间字符串 (MM-dd HH:mm)
     */
    public static String formatStartTime(long timestampMs) {
        try {
            Instant instant = Instant.ofEpochMilli(timestampMs);
            LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            return dateTime.format(DATE_TIME_FORMATTER);
        } catch (Exception e) {
            return "时间错误";
        }
    }
    
    /**
     * 将毫秒时间戳转换为完整日期时间格式
     * @param timestampMs 毫秒时间戳
     * @return 格式化后的时间字符串 (yyyy-MM-dd HH:mm:ss)
     */
    public static String formatFullDateTime(long timestampMs) {
        try {
            Instant instant = Instant.ofEpochMilli(timestampMs);
            LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            return dateTime.format(FULL_DATE_TIME_FORMATTER);
        } catch (Exception e) {
            return "时间错误";
        }
    }
    
    /**
     * 格式化比赛进行时间
     * @param matchTime 比赛时间字符串 (如 "36'", "45+2'")
     * @param period 比赛阶段 (如 "1H", "2H", "HT")
     * @return 格式化后的比赛时间
     */
    public static String formatMatchTime(String matchTime, String period) {
        if (matchTime == null || matchTime.trim().isEmpty()) {
            return formatPeriodOnly(period);
        }
        
        if (period == null || period.trim().isEmpty()) {
            return matchTime.trim();
        }
        
        String formattedPeriod = formatPeriod(period.trim());
        return matchTime.trim() + " " + formattedPeriod;
    }
    
    /**
     * 格式化比赛阶段
     * @param period 原始阶段字符串
     * @return 格式化后的阶段字符串
     */
    private static String formatPeriod(String period) {
        switch (period.toUpperCase()) {
            case "1H":
                return "上半场";
            case "2H":
                return "下半场";
            case "HT":
                return "中场";
            case "FT":
                return "全场结束";
            case "ET":
                return "加时赛";
            case "PEN":
                return "点球大战";
            default:
                return period;
        }
    }
    
    /**
     * 仅格式化比赛阶段
     * @param period 比赛阶段
     * @return 格式化后的阶段字符串
     */
    private static String formatPeriodOnly(String period) {
        if (period == null || period.trim().isEmpty()) {
            return "";
        }
        return formatPeriod(period.trim());
    }
    
    /**
     * 格式化比赛状态对应的时间显示
     * @param status 比赛状态
     * @param matchTime 比赛时间
     * @param period 比赛阶段
     * @return 格式化后的时间显示
     */
    public static String formatMatchTimeByStatus(String status, String matchTime, String period) {
        if (status == null) {
            return formatMatchTime(matchTime, period);
        }
        
        switch (status) {
            case "未开始":
            case "待定":
                return "未开始";
            case "已结束":
            case "全场结束":
                return "已结束";
            case "中场休息":
                return "中场休息";
            case "延期":
                return "延期";
            case "取消":
                return "取消";
            case "进行中":
            default:
                return formatMatchTime(matchTime, period);
        }
    }
    
    /**
     * 解析并格式化时间字符串
     * @param timeStr 时间字符串 (可能包含分钟和阶段信息)
     * @return 格式化后的时间字符串
     */
    public static String parseAndFormatTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return "";
        }
        
        String[] parts = timeStr.trim().split("\\s+");
        if (parts.length >= 2) {
            return formatMatchTime(parts[0], parts[1]);
        } else if (parts.length == 1) {
            // 判断是时间还是阶段
            String part = parts[0];
            if (part.contains("'") || part.matches("\\d+")) {
                return part; // 是时间
            } else {
                return formatPeriod(part); // 是阶段
            }
        }
        
        return timeStr;
    }
    
    /**
     * 获取当前时间的格式化字符串
     * @return 当前时间 (HH:mm)
     */
    public static String getCurrentTime() {
        return LocalDateTime.now().format(TIME_ONLY_FORMATTER);
    }
    
    /**
     * 获取当前完整日期时间的格式化字符串
     * @return 当前日期时间 (yyyy-MM-dd HH:mm:ss)
     */
    public static String getCurrentFullDateTime() {
        return LocalDateTime.now().format(FULL_DATE_TIME_FORMATTER);
    }
    
    /**
     * 验证时间戳是否有效
     * @param timestampMs 毫秒时间戳
     * @return 是否有效
     */
    public static boolean isValidTimestamp(long timestampMs) {
        try {
            // 检查时间戳是否在合理范围内 (1970年到2100年)
            return timestampMs > 0 && timestampMs < 4102444800000L;
        } catch (Exception e) {
            return false;
        }
    }
}
