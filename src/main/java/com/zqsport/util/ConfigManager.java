package com.zqsport.util;

import java.io.*;
import java.util.Properties;

/**
 * 配置管理器
 * 用于保存和读取用户配置信息（如账号密码）
 */
public class ConfigManager {
    
    private static final String CONFIG_FILE = "config.properties";
    private static final String URL_KEY = "login.url";
    private static final String USERNAME_KEY = "login.username";
    private static final String PASSWORD_KEY = "login.password";
    private static final String REMEMBER_KEY = "login.remember";
    
    // HTTP轮询配置键
    private static final String HTTP_POLLING_ENABLED_KEY = "http.polling.enabled";
    private static final String HTTP_POLLING_INTERVAL_KEY = "http.polling.interval";
    private static final String HTTP_POLLING_TIMEOUT_KEY = "http.polling.timeout";
    private static final String HTTP_POLLING_RETRY_COUNT_KEY = "http.polling.retry.count";
    private static final String HTTP_POLLING_RETRY_DELAY_KEY = "http.polling.retry.delay";
    
    // 体育数据配置键
    private static final String SPORTS_DEFAULT_SPORT_ID_KEY = "sports.default.sport_id";
    private static final String SPORTS_DEFAULT_LOCALE_KEY = "sports.default.locale";
    private static final String SPORTS_DEFAULT_MARKET_TYPE_KEY = "sports.default.market_type";
    private static final String SPORTS_DEFAULT_ODDS_TYPE_KEY = "sports.default.odds_type";
    
    private Properties properties;
    
    public ConfigManager() {
        properties = new Properties();
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        File configFile = new File(CONFIG_FILE);
        if (configFile.exists()) {
            try (FileInputStream fis = new FileInputStream(configFile)) {
                properties.load(fis);
                System.out.println("✅ 配置文件加载成功");
            } catch (IOException e) {
                System.out.println("⚠️ 配置文件加载失败: " + e.getMessage());
            }
        } else {
            System.out.println("📋 配置文件不存在，将使用默认配置");
        }
    }
    
    /**
     * 保存配置文件
     */
    private void saveConfig() {
        try (FileOutputStream fos = new FileOutputStream(CONFIG_FILE)) {
            properties.store(fos, "Sports Monitor Configuration");
            System.out.println("✅ 配置文件保存成功");
        } catch (IOException e) {
            System.out.println("❌ 配置文件保存失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取登录URL
     */
    public String getLoginUrl() {
        return properties.getProperty(URL_KEY, "https://www.pin975.com");
    }
    
    /**
     * 设置登录URL
     */
    public void setLoginUrl(String url) {
        properties.setProperty(URL_KEY, url);
    }
    
    /**
     * 获取用户名
     */
    public String getUsername() {
        return properties.getProperty(USERNAME_KEY, "");
    }
    
    /**
     * 设置用户名
     */
    public void setUsername(String username) {
        properties.setProperty(USERNAME_KEY, username != null ? username : "");
    }
    
    /**
     * 获取密码
     */
    public String getPassword() {
        return properties.getProperty(PASSWORD_KEY, "");
    }
    
    /**
     * 设置密码
     */
    public void setPassword(String password) {
        properties.setProperty(PASSWORD_KEY, password != null ? password : "");
    }
    
    /**
     * 获取是否记住密码
     */
    public boolean isRememberPassword() {
        return Boolean.parseBoolean(properties.getProperty(REMEMBER_KEY, "false"));
    }
    
    /**
     * 设置是否记住密码
     */
    public void setRememberPassword(boolean remember) {
        properties.setProperty(REMEMBER_KEY, String.valueOf(remember));
    }
    
    /**
     * 保存登录信息
     */
    public void saveLoginInfo(String url, String username, String password, boolean remember) {
        setLoginUrl(url);
        setUsername(username);
        setRememberPassword(remember);
        
        if (remember) {
            setPassword(password);
        } else {
            setPassword(""); // 如果不记住密码，清空保存的密码
        }
        
        saveConfig();
    }
    
    /**
     * 清除保存的登录信息
     */
    public void clearLoginInfo() {
        setUsername("");
        setPassword("");
        setRememberPassword(false);
        saveConfig();
    }
    
    /**
     * 获取配置文件路径
     */
    public String getConfigFilePath() {
        return new File(CONFIG_FILE).getAbsolutePath();
    }
    
    // HTTP轮询配置方法
    
    /**
     * 获取是否启用HTTP轮询
     */
    public boolean isHttpPollingEnabled() {
        return Boolean.parseBoolean(properties.getProperty(HTTP_POLLING_ENABLED_KEY, "true"));
    }
    
    /**
     * 设置是否启用HTTP轮询
     */
    public void setHttpPollingEnabled(boolean enabled) {
        properties.setProperty(HTTP_POLLING_ENABLED_KEY, String.valueOf(enabled));
    }
    
    /**
     * 获取HTTP轮询间隔（毫秒）
     */
    public int getHttpPollingInterval() {
        return Integer.parseInt(properties.getProperty(HTTP_POLLING_INTERVAL_KEY, "5000"));
    }
    
    /**
     * 设置HTTP轮询间隔（毫秒）
     */
    public void setHttpPollingInterval(int interval) {
        properties.setProperty(HTTP_POLLING_INTERVAL_KEY, String.valueOf(interval));
    }
    
    /**
     * 获取HTTP请求超时时间（毫秒）
     */
    public int getHttpPollingTimeout() {
        return Integer.parseInt(properties.getProperty(HTTP_POLLING_TIMEOUT_KEY, "30000"));
    }
    
    /**
     * 设置HTTP请求超时时间（毫秒）
     */
    public void setHttpPollingTimeout(int timeout) {
        properties.setProperty(HTTP_POLLING_TIMEOUT_KEY, String.valueOf(timeout));
    }
    
    /**
     * 获取HTTP重试次数
     */
    public int getHttpRetryCount() {
        return Integer.parseInt(properties.getProperty(HTTP_POLLING_RETRY_COUNT_KEY, "3"));
    }
    
    /**
     * 设置HTTP重试次数
     */
    public void setHttpRetryCount(int count) {
        properties.setProperty(HTTP_POLLING_RETRY_COUNT_KEY, String.valueOf(count));
    }
    
    /**
     * 获取HTTP重试延迟（毫秒）
     */
    public int getHttpRetryDelay() {
        return Integer.parseInt(properties.getProperty(HTTP_POLLING_RETRY_DELAY_KEY, "2000"));
    }
    
    /**
     * 设置HTTP重试延迟（毫秒）
     */
    public void setHttpRetryDelay(int delay) {
        properties.setProperty(HTTP_POLLING_RETRY_DELAY_KEY, String.valueOf(delay));
    }
    
    // 体育数据配置方法
    
    /**
     * 获取默认体育ID
     */
    public int getDefaultSportId() {
        return Integer.parseInt(properties.getProperty(SPORTS_DEFAULT_SPORT_ID_KEY, "29"));
    }
    
    /**
     * 设置默认体育ID
     */
    public void setDefaultSportId(int sportId) {
        properties.setProperty(SPORTS_DEFAULT_SPORT_ID_KEY, String.valueOf(sportId));
    }
    
    /**
     * 获取默认语言区域
     */
    public String getDefaultLocale() {
        return properties.getProperty(SPORTS_DEFAULT_LOCALE_KEY, "zh_CN");
    }
    
    /**
     * 设置默认语言区域
     */
    public void setDefaultLocale(String locale) {
        properties.setProperty(SPORTS_DEFAULT_LOCALE_KEY, locale != null ? locale : "zh_CN");
    }
    
    /**
     * 获取默认市场类型
     */
    public int getDefaultMarketType() {
        return Integer.parseInt(properties.getProperty(SPORTS_DEFAULT_MARKET_TYPE_KEY, "1"));
    }
    
    /**
     * 设置默认市场类型
     */
    public void setDefaultMarketType(int marketType) {
        properties.setProperty(SPORTS_DEFAULT_MARKET_TYPE_KEY, String.valueOf(marketType));
    }
    
    /**
     * 获取默认赔率类型
     */
    public int getDefaultOddsType() {
        return Integer.parseInt(properties.getProperty(SPORTS_DEFAULT_ODDS_TYPE_KEY, "2"));
    }
    
    /**
     * 设置默认赔率类型
     */
    public void setDefaultOddsType(int oddsType) {
        properties.setProperty(SPORTS_DEFAULT_ODDS_TYPE_KEY, String.valueOf(oddsType));
    }
    
    /**
     * 保存HTTP轮询配置
     */
    public void saveHttpPollingConfig(boolean enabled, int interval, int timeout, int retryCount, int retryDelay) {
        setHttpPollingEnabled(enabled);
        setHttpPollingInterval(interval);
        setHttpPollingTimeout(timeout);
        setHttpRetryCount(retryCount);
        setHttpRetryDelay(retryDelay);
        saveConfig();
    }
    
    /**
     * 保存体育数据配置
     */
    public void saveSportsConfig(int sportId, String locale, int marketType, int oddsType) {
        setDefaultSportId(sportId);
        setDefaultLocale(locale);
        setDefaultMarketType(marketType);
        setDefaultOddsType(oddsType);
        saveConfig();
    }
}
