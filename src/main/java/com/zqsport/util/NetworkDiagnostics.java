package com.zqsport.util;

import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * 网络诊断和连接工具类
 */
public class NetworkDiagnostics {
    
    private static final Logger logger = LoggerFactory.getLogger(NetworkDiagnostics.class);
    
    /**
     * 创建宽松SSL配置的HTTP客户端（忽略证书验证）
     */
    public static CloseableHttpClient createRelaxedSslClient() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        // 创建宽松的SSL上下文，信任所有证书
        SSLContext sslContext = SSLContextBuilder
                .create()
                .loadTrustMaterial(new TrustAllStrategy())
                .build();
        
        // 创建宽松的SSL连接工厂
        SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                sslContext,
                (hostname, session) -> true // 允许所有主机名
        );
        
        // 注册协议
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder
                .<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", sslSocketFactory)
                .build();
        
        // 创建连接管理器
        PoolingHttpClientConnectionManager connectionManager = 
                new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        connectionManager.setMaxTotal(50);
        connectionManager.setDefaultMaxPerRoute(20);
        connectionManager.setValidateAfterInactivity(org.apache.hc.core5.util.TimeValue.ofSeconds(30));
        
        // 配置请求参数
        RequestConfig config = RequestConfig.custom()
                .setConnectionRequestTimeout(org.apache.hc.core5.util.Timeout.ofSeconds(10))
                .setResponseTimeout(org.apache.hc.core5.util.Timeout.ofSeconds(30))
                .setConnectTimeout(org.apache.hc.core5.util.Timeout.ofSeconds(10))
                .build();
        
        return HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(config)
                .build();
    }
    
    /**
     * 网络连接诊断
     */
    public static boolean diagnoseConnection(String url) {
        logger.info("🔍 开始网络连接诊断...");
        logger.info("目标URL: {}", url);
        
        boolean success = false;
        
        // 测试1：基本连接
        logger.info("\n=== 测试1: 基本HTTP连接 ===");
        success = testBasicConnection(url) || success;
        
        // 测试2：宽松SSL连接
        logger.info("\n=== 测试2: 宽松SSL连接 ===");
        success = testRelaxedSslConnection(url) || success;
        
        // 测试3：完整浏览器模拟
        logger.info("\n=== 测试3: 完整浏览器模拟 ===");
        success = testBrowserSimulation(url) || success;
        
        logger.info("\n🏁 诊断完成，至少一个测试成功: {}", success);
        return success;
    }
    
    /**
     * 测试基本HTTP连接
     */
    private static boolean testBasicConnection(String url) {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            try (CloseableHttpResponse response = client.execute(request)) {
                int statusCode = response.getCode();
                logger.info("✅ 基本连接成功，状态码: {}", statusCode);
                return statusCode >= 200 && statusCode < 400;
            }
        } catch (Exception e) {
            logger.warn("❌ 基本连接失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试宽松SSL连接
     */
    private static boolean testRelaxedSslConnection(String url) {
        try (CloseableHttpClient client = createRelaxedSslClient()) {
            HttpGet request = new HttpGet(url);
            setupBrowserHeaders(request);
            
            try (CloseableHttpResponse response = client.execute(request)) {
                int statusCode = response.getCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                logger.info("✅ 宽松SSL连接成功，状态码: {}", statusCode);
                logger.info("响应长度: {} 字符", responseBody.length());
                
                if (responseBody.length() > 100) {
                    logger.info("响应预览: {}", responseBody.substring(0, 100) + "...");
                }
                
                return statusCode >= 200 && statusCode < 400;
            }
        } catch (Exception e) {
            logger.warn("❌ 宽松SSL连接失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试完整浏览器模拟
     */
    private static boolean testBrowserSimulation(String url) {
        try (CloseableHttpClient client = createRelaxedSslClient()) {
            HttpGet request = new HttpGet(url);
            setupFullBrowserHeaders(request);
            
            try (CloseableHttpResponse response = client.execute(request)) {
                int statusCode = response.getCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                logger.info("✅ 完整浏览器模拟成功，状态码: {}", statusCode);
                logger.info("响应长度: {} 字符", responseBody.length());
                
                // 检查响应内容
                if (responseBody.contains("Access Denied") || responseBody.contains("403") || responseBody.contains("blocked")) {
                    logger.warn("⚠️ 响应可能包含访问拒绝信息");
                } else if (responseBody.contains("{") || responseBody.contains("[")) {
                    logger.info("✅ 响应似乎包含JSON数据");
                }
                
                return statusCode >= 200 && statusCode < 400;
            }
        } catch (Exception e) {
            logger.warn("❌ 完整浏览器模拟失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 设置基本浏览器请求头
     */
    private static void setupBrowserHeaders(HttpGet request) {
        request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0");
        request.setHeader("Accept", "application/json, text/plain, */*");
        request.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        request.setHeader("Accept-Encoding", "gzip, deflate, br");
        request.setHeader("Cache-Control", "no-cache");
        request.setHeader("Pragma", "no-cache");
    }
    
    /**
     * 设置完整浏览器请求头
     */
    private static void setupFullBrowserHeaders(HttpGet request) {
        // 基本头
        setupBrowserHeaders(request);
        
        // 安全头
        request.setHeader("Sec-Fetch-Dest", "empty");
        request.setHeader("Sec-Fetch-Mode", "cors");
        request.setHeader("Sec-Fetch-Site", "same-origin");
        request.setHeader("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Microsoft Edge\";v=\"120\"");
        request.setHeader("Sec-Ch-Ua-Mobile", "?0");
        request.setHeader("Sec-Ch-Ua-Platform", "\"Windows\"");
        
        // 其他头
        request.setHeader("DNT", "1");
        request.setHeader("Connection", "keep-alive");
        request.setHeader("Upgrade-Insecure-Requests", "1");
        
        // 站点特定头
        request.setHeader("Origin", "https://www.ps3838.com");
        request.setHeader("Referer", "https://www.ps3838.com/zh-cn/sports/soccer");
    }
    
    /**
     * 快速网络连接测试
     */
    public static void quickTest() {
        String[] testUrls = {
            "https://www.google.com",
            "https://www.baidu.com", 
            "https://www.ps3838.com",
            "https://www.ps3838.com/sports-service/sv/compact/events?sp=29&locale=zh_CN"
        };
        
        logger.info("🚀 开始快速网络测试...");
        
        for (String url : testUrls) {
            logger.info("\n测试URL: {}", url);
            boolean success = diagnoseConnection(url);
            logger.info("结果: {}", success ? "成功" : "失败");
        }
    }
    
    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        logger.info("=== 网络诊断工具 ===");
        
        if (args.length > 0) {
            // 测试指定URL
            String url = args[0];
            diagnoseConnection(url);
        } else {
            // 执行快速测试
            quickTest();
        }
    }
}