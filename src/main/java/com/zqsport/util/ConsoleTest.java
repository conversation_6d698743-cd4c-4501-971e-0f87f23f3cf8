package com.zqsport.util;

import java.io.PrintStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;

/**
 * 控制台中文输出测试
 */
public class ConsoleTest {
    
    public static void main(String[] args) {
        // 显示当前编码信息
        System.out.println("=== 编码信息检查 ===");
        System.out.println("Default Charset: " + Charset.defaultCharset());
        System.out.println("File Encoding: " + System.getProperty("file.encoding"));
        System.out.println("Console Encoding: " + System.getProperty("console.encoding"));
        System.out.println("OS Name: " + System.getProperty("os.name"));
        System.out.println();
        
        // 测试中文输出
        System.out.println("=== 中文输出测试 ===");
        System.out.println("🌐 网络连接测试");
        System.out.println("✅ 连接成功");
        System.out.println("❌ 连接失败");
        System.out.println("⚠️ 警告信息");
        System.out.println("🔍 正在检查...");
        System.out.println("📍 测试目标：https://www.ps3838.com");
        System.out.println("💡 建议：检查网络设置");
        System.out.println("🎉 测试完成！");
        System.out.println();
        
        // 尝试设置UTF-8输出
        try {
            System.setOut(new PrintStream(System.out, true, "UTF-8"));
            System.out.println("=== UTF-8编码输出测试 ===");
            System.out.println("这是UTF-8编码的中文输出测试");
            System.out.println("体育数据API连接测试");
            System.out.println("网络诊断程序");
        } catch (UnsupportedEncodingException e) {
            System.out.println("UTF-8编码设置失败: " + e.getMessage());
        }
        
        System.out.println();
        System.out.println("如果您看到正确的中文字符，说明编码配置正确！");
    }
}
