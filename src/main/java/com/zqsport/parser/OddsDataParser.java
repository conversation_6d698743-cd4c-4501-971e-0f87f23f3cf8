package com.zqsport.parser;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zqsport.model.OddsData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 赔率数据解析器
 */
public class OddsDataParser {
    
    private static final Logger logger = LoggerFactory.getLogger(OddsDataParser.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 解析JSON响应数据
     */
    public List<OddsData> parseOddsResponse(String jsonResponse) {
        List<OddsData> oddsList = new ArrayList<>();
        
        if (jsonResponse == null || jsonResponse.trim().isEmpty()) {
            logger.warn("JSON响应为空");
            return oddsList;
        }
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = objectMapper.readValue(jsonResponse, Map.class);
            
            // 打印响应结构信息
            printResponseStructure(responseMap);
            
            // 解析events数组
            if (responseMap.containsKey("events")) {
                Object eventsObj = responseMap.get("events");
                if (eventsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> eventsList = (List<Map<String, Object>>) eventsObj;
                    
                    logger.info("🎯 开始解析 {} 个赛事", eventsList.size());
                    
                    for (Map<String, Object> eventMap : eventsList) {
                        try {
                            OddsData oddsData = parseEventData(eventMap);
                            if (oddsData != null) {
                                oddsList.add(oddsData);
                            }
                        } catch (Exception e) {
                            logger.warn("解析单个赛事数据失败: {}", e.getMessage());
                        }
                    }
                }
            } else {
                logger.warn("响应中未找到events字段");
            }
            
            logger.info("✅ 成功解析 {} 个赛事数据", oddsList.size());
            
        } catch (Exception e) {
            logger.error("解析JSON响应失败", e);
        }
        
        return oddsList;
    }
    
    /**
     * 解析单个赛事数据
     */
    private OddsData parseEventData(Map<String, Object> eventMap) {
        try {
            OddsData oddsData = new OddsData();
            
            // 基本信息
            oddsData.setEventId(getString(eventMap, "id"));
            oddsData.setEventName(getString(eventMap, "des"));
            oddsData.setStatus(getString(eventMap, "status"));
            
            // 解析队伍信息
            parseTeamInfo(eventMap, oddsData);
            
            // 解析时间信息
            parseTimeInfo(eventMap, oddsData);
            
            // 解析比分信息
            parseScoreInfo(eventMap, oddsData);
            
            // 解析赔率信息
            parseOddsInfo(eventMap, oddsData);
            
            // 解析联赛信息
            parseLeagueInfo(eventMap, oddsData);
            
            return oddsData;
            
        } catch (Exception e) {
            logger.warn("解析赛事数据失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析队伍信息
     */
    private void parseTeamInfo(Map<String, Object> eventMap, OddsData oddsData) {
        // 尝试从不同字段解析队伍信息
        String homeTeam = getString(eventMap, "home");
        String awayTeam = getString(eventMap, "away");
        
        if (homeTeam == null || awayTeam == null) {
            // 尝试从participants解析
            Object participants = eventMap.get("participants");
            if (participants instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> partList = (List<Map<String, Object>>) participants;
                if (partList.size() >= 2) {
                    homeTeam = getString(partList.get(0), "name");
                    awayTeam = getString(partList.get(1), "name");
                }
            }
        }
        
        oddsData.setHomeTeam(homeTeam);
        oddsData.setAwayTeam(awayTeam);
    }
    
    /**
     * 解析时间信息
     */
    private void parseTimeInfo(Map<String, Object> eventMap, OddsData oddsData) {
        // 尝试解析开始时间
        Object startTimeObj = eventMap.get("starts");
        if (startTimeObj == null) {
            startTimeObj = eventMap.get("startTime");
        }
        
        if (startTimeObj instanceof Number) {
            long timestamp = ((Number) startTimeObj).longValue();
            // 如果是毫秒时间戳
            if (timestamp > 1000000000000L) {
                oddsData.setStartTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault()));
            } else {
                // 如果是秒时间戳
                oddsData.setStartTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault()));
            }
        }
    }
    
    /**
     * 解析比分信息
     */
    private void parseScoreInfo(Map<String, Object> eventMap, OddsData oddsData) {
        Object score = eventMap.get("score");
        if (score instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> scoreMap = (Map<String, Object>) score;
            
            Object home = scoreMap.get("home");
            Object away = scoreMap.get("away");
            
            if (home instanceof Number) {
                oddsData.setScore1(((Number) home).intValue());
            }
            if (away instanceof Number) {
                oddsData.setScore2(((Number) away).intValue());
            }
        }
    }
    
    /**
     * 解析赔率信息
     */
    private void parseOddsInfo(Map<String, Object> eventMap, OddsData oddsData) {
        Map<String, Double> oddsMap = new HashMap<>();
        
        // 尝试从不同字段解析赔率
        Object periods = eventMap.get("periods");
        if (periods instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> periodsList = (List<Map<String, Object>>) periods;
            
            for (Map<String, Object> period : periodsList) {
                Object moneyline = period.get("moneyline");
                if (moneyline instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> mlMap = (Map<String, Object>) moneyline;
                    
                    // 解析1X2赔率
                    parseMoneylineOdds(mlMap, oddsMap);
                }
                
                // 解析其他类型赔率
                Object spreads = period.get("spreads");
                if (spreads instanceof List) {
                    parseSpreadOdds(spreads, oddsMap);
                }
                
                Object totals = period.get("totals");
                if (totals instanceof List) {
                    parseTotalOdds(totals, oddsMap);
                }
            }
        }
        
        oddsData.setOdds(oddsMap);
    }
    
    /**
     * 解析胜负赔率
     */
    private void parseMoneylineOdds(Map<String, Object> mlMap, Map<String, Double> oddsMap) {
        Object home = mlMap.get("home");
        Object away = mlMap.get("away");
        Object draw = mlMap.get("draw");
        
        if (home instanceof Number) {
            oddsMap.put("1", convertToDecimalOdds(((Number) home).doubleValue()));
        }
        if (draw instanceof Number) {
            oddsMap.put("X", convertToDecimalOdds(((Number) draw).doubleValue()));
        }
        if (away instanceof Number) {
            oddsMap.put("2", convertToDecimalOdds(((Number) away).doubleValue()));
        }
    }
    
    /**
     * 解析让分赔率
     */
    private void parseSpreadOdds(Object spreads, Map<String, Double> oddsMap) {
        // 实现让分赔率解析逻辑
    }
    
    /**
     * 解析大小球赔率
     */
    private void parseTotalOdds(Object totals, Map<String, Double> oddsMap) {
        // 实现大小球赔率解析逻辑
    }
    
    /**
     * 解析联赛信息
     */
    private void parseLeagueInfo(Map<String, Object> eventMap, OddsData oddsData) {
        String league = getString(eventMap, "league");
        if (league == null) {
            league = getString(eventMap, "competition");
        }
        oddsData.setLeague(league);
    }
    
    /**
     * 安全获取字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 转换为欧洲赔率格式
     */
    private double convertToDecimalOdds(double americanOdds) {
        if (americanOdds > 0) {
            return (americanOdds / 100.0) + 1.0;
        } else {
            return (100.0 / Math.abs(americanOdds)) + 1.0;
        }
    }
    
    /**
     * 打印响应结构信息
     */
    private void printResponseStructure(Map<String, Object> responseMap) {
        logger.info("📋 响应数据结构:");
        for (String key : responseMap.keySet()) {
            Object value = responseMap.get(key);
            String type = value != null ? value.getClass().getSimpleName() : "null";
            logger.info("  {} -> {}", key, type);
        }
    }
}
