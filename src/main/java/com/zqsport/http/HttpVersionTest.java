package com.zqsport.http;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * HTTP版本功能测试
 */
public class HttpVersionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpVersionTest.class);
    
    public static void main(String[] args) {
        logger.info("=== HTTP版本功能测试开始 ===");
        
        // 测试1：HTTP客户端基本功能
        testHttpClient();
        
        // 测试2：HTTP管理器功能
        testHttpManager();
        
        // 测试3：配置参数测试
        testConfiguration();
        
        logger.info("=== HTTP版本功能测试完成 ===");
    }
    
    /**
     * 测试HTTP客户端基本功能
     */
    private static void testHttpClient() {
        logger.info("--- 测试HTTP客户端 ---");
        
        SportsHttpClient client = new SportsHttpClient();
        
        try {
            // 测试无认证模式
            logger.info("测试无认证模式请求...");
            String response = client.getOddsData(29, "zh_CN");
            
            if (response != null) {
                logger.info("✅ HTTP客户端测试成功，响应长度: {}", response.length());
            } else {
                logger.warn("⚠️ HTTP客户端测试返回空响应（可能是认证问题）");
            }
            
        } catch (Exception e) {
            logger.error("❌ HTTP客户端测试失败", e);
        } finally {
            client.close();
        }
    }
    
    /**
     * 测试HTTP管理器功能
     */
    private static void testHttpManager() {
        logger.info("--- 测试HTTP管理器 ---");
        
        SportsHttpManager manager = new SportsHttpManager();
        
        try {
            // 测试连接
            logger.info("测试管理器连接...");
            boolean connected = manager.connect();
            
            if (connected) {
                logger.info("✅ 管理器连接成功");
                
                // 测试轮询设置
                manager.setPollingInterval(10000); // 10秒
                logger.info("✅ 轮询间隔设置成功");
                
                // 测试订阅
                manager.subscribeToOdds(29, "zh_CN");
                logger.info("✅ 订阅设置成功");
                
                // 等待几秒观察轮询
                logger.info("等待5秒观察轮询效果...");
                Thread.sleep(5000);
                
                // 打印统计信息
                manager.printStatistics();
                
                // 停止轮询
                manager.stopPolling();
                logger.info("✅ 轮询停止成功");
                
            } else {
                logger.warn("⚠️ 管理器连接失败");
            }
            
        } catch (Exception e) {
            logger.error("❌ HTTP管理器测试失败", e);
        } finally {
            manager.disconnect();
        }
    }
    
    /**
     * 测试配置参数
     */
    private static void testConfiguration() {
        logger.info("--- 测试配置参数 ---");
        
        try {
            // 测试URL构建
            String apiUrl = HttpConfig.buildOddsApiUrl();
            logger.info("API URL: {}", apiUrl);
            
            // 测试查询参数构建
            String queryParams = HttpConfig.buildQueryParams(29, "zh_CN");
            logger.info("查询参数: {}", queryParams);
            
            // 测试轮询间隔配置
            logger.info("默认轮询间隔: {}ms", HttpConfig.DEFAULT_POLLING_INTERVAL);
            logger.info("最小轮询间隔: {}ms", HttpConfig.MIN_POLLING_INTERVAL);
            logger.info("最大轮询间隔: {}ms", HttpConfig.MAX_POLLING_INTERVAL);
            
            logger.info("✅ 配置参数测试完成");
            
        } catch (Exception e) {
            logger.error("❌ 配置参数测试失败", e);
        }
    }
}
