package com.zqsport.http;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.UnknownHostException;
import java.net.ConnectException;
import javax.net.ssl.SSLException;

/**
 * 基础网络测试（使用Java内置HTTP客户端）
 */
public class BasicNetworkTest {
    
    public static void main(String[] args) {
        System.out.println("🌐 开始基础网络连接测试...");
        System.out.println("=".repeat(60));
        
        // 测试目标URL
        String[] testUrls = {
            "https://www.baidu.com",
            "https://httpbin.org/get",  // 测试API端点
            "https://www.ps3838.com",
            "https://www.ps3838.com/sports-service/sv/compact/events"
        };
        
        boolean anySuccess = false;
        
        for (String urlStr : testUrls) {
            System.out.println("\n📍 测试URL: " + urlStr);
            boolean success = testConnection(urlStr);
            anySuccess = anySuccess || success;
        }
        
        System.out.println("\n" + "=".repeat(60));
        if (anySuccess) {
            System.out.println("🎉 网络连接正常！至少有一个URL连接成功。");
            System.out.println("💡 如果体育数据API连接失败，可能需要：");
            System.out.println("   1. 添加认证信息（token、cookie等）");
            System.out.println("   2. 使用更完整的请求头模拟浏览器");
            System.out.println("   3. 处理SSL证书问题");
        } else {
            System.out.println("❌ 所有连接都失败了！");
            System.out.println("💡 建议检查：");
            System.out.println("   1. 网络连接是否正常");
            System.out.println("   2. 防火墙或代理设置");
            System.out.println("   3. DNS解析是否正常");
        }
        System.out.println("=".repeat(60));
    }
    
    private static boolean testConnection(String urlStr) {
        try {
            URL url = new URL(urlStr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 设置基本请求属性
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(15000);    // 15秒读取超时
            
            // 设置请求头模拟浏览器
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            connection.setRequestProperty("Accept", "application/json, text/plain, */*");
            connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            connection.setRequestProperty("Cache-Control", "no-cache");
            
            System.out.print("   连接中... ");
            
            // 建立连接
            connection.connect();
            
            int responseCode = connection.getResponseCode();
            System.out.print("状态码: " + responseCode + " ");
            
            if (responseCode >= 200 && responseCode < 300) {
                System.out.println("✅ 成功");
                
                // 读取响应内容
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                int lineCount = 0;
                
                while ((line = reader.readLine()) != null && lineCount < 5) {
                    response.append(line).append("\n");
                    lineCount++;
                }
                reader.close();
                
                String responseStr = response.toString();
                System.out.println("   响应长度: " + responseStr.length() + " 字符");
                
                if (responseStr.contains("{") || responseStr.contains("[")) {
                    System.out.println("   ✅ 响应包含JSON数据");
                } else if (responseStr.toLowerCase().contains("html")) {
                    System.out.println("   📄 响应是HTML页面");
                }
                
                // 显示响应预览
                if (responseStr.length() > 100) {
                    System.out.println("   预览: " + responseStr.substring(0, 100).replace("\n", " ") + "...");
                }
                
                return true;
                
            } else if (responseCode >= 300 && responseCode < 400) {
                System.out.println("🔄 重定向");
                String location = connection.getHeaderField("Location");
                if (location != null) {
                    System.out.println("   重定向到: " + location);
                }
                return true; // 重定向也算成功
                
            } else {
                System.out.println("⚠️ 客户端/服务器错误");
                
                // 尝试读取错误响应
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                    StringBuilder errorResponse = new StringBuilder();
                    String line;
                    int lineCount = 0;
                    while ((line = errorReader.readLine()) != null && lineCount < 3) {
                        errorResponse.append(line).append(" ");
                        lineCount++;
                    }
                    if (errorResponse.length() > 0) {
                        System.out.println("   错误信息: " + errorResponse.toString().substring(0, Math.min(150, errorResponse.length())));
                    }
                } catch (Exception e) {
                    // 忽略读取错误响应的异常
                }
                
                return false;
            }
            
        } catch (UnknownHostException e) {
            System.out.println("❌ 域名解析失败");
            System.out.println("   💡 请检查DNS设置或网络连接");
            return false;
            
        } catch (ConnectException e) {
            System.out.println("❌ 连接被拒绝");
            System.out.println("   💡 请检查防火墙设置或目标服务器状态");
            return false;
            
        } catch (SSLException e) {
            System.out.println("❌ SSL握手失败");
            System.out.println("   💡 SSL证书问题，可能需要信任证书或使用HTTP");
            return false;
            
        } catch (IOException e) {
            System.out.println("❌ IO异常: " + e.getMessage());
            
            if (e.getMessage().contains("timeout")) {
                System.out.println("   💡 连接超时，网络可能较慢");
            } else if (e.getMessage().contains("reset")) {
                System.out.println("   💡 连接被重置，可能被防火墙阻止");
            }
            
            return false;
            
        } catch (Exception e) {
            System.out.println("❌ 其他错误: " + e.getMessage());
            return false;
        }
    }
}
