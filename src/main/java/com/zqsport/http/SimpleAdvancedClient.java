package com.zqsport.http;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.Scanner;

/**
 * 简化版高级HTTP客户端
 * 使用Java内置HTTP客户端，避免外部依赖问题
 */
public class SimpleAdvancedClient {
    
    private Random random = new Random();
    
    // 多个User-Agent轮换使用
    private final List<String> userAgents = Arrays.asList(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"
    );
    
    static {
        // 设置信任所有SSL证书
        setupTrustAllSSL();
    }
    
    /**
     * 设置信任所有SSL证书
     */
    private static void setupTrustAllSSL() {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
            };
            
            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            
            // 创建信任所有主机名的HostnameVerifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };
            
            // 安装HostnameVerifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            
            System.out.println("SSL certificate verification disabled");
            
        } catch (Exception e) {
            System.err.println("Failed to setup SSL trust: " + e.getMessage());
        }
    }
    
    /**
     * 执行GET请求
     */
    public String executeGet(String urlStr, String cookieHeader) {
        try {
            URL url = new URL(urlStr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求方法和超时
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000);    // 60秒读取超时
            
            // 设置高级请求头
            setupAdvancedHeaders(connection, cookieHeader);
            
            System.out.println("Executing enhanced request to: " + urlStr);
            
            // 建立连接
            connection.connect();
            
            int responseCode = connection.getResponseCode();
            System.out.println("Response status: " + responseCode);
            
            // 读取响应
            String responseBody = readResponse(connection, responseCode);
            
            if (responseCode >= 200 && responseCode < 300) {
                System.out.println("SUCCESS: Request completed successfully");
                System.out.println("Response length: " + responseBody.length() + " characters");
                return responseBody;
            } else {
                System.out.println("WARNING: Unexpected status code: " + responseCode);
                if (responseBody.length() > 0) {
                    System.out.println("Response preview: " + 
                        responseBody.substring(0, Math.min(200, responseBody.length())));
                }
                return responseBody;
            }
            
        } catch (Exception e) {
            System.err.println("Request failed: " + e.getMessage());
            
            // 提供详细的错误诊断
            if (e.getMessage().contains("Connection reset")) {
                System.err.println("\n=== CONNECTION RESET ANALYSIS ===");
                System.err.println("This error indicates the server actively rejected the connection.");
                System.err.println("Common causes:");
                System.err.println("1. Anti-bot protection detected automated request");
                System.err.println("2. Missing required authentication (cookies/tokens)");
                System.err.println("3. Server-side firewall or rate limiting");
                System.err.println("4. Incorrect request headers or User-Agent");
                System.err.println("\nSUGGESTED SOLUTIONS:");
                System.err.println("✓ Copy complete Cookie header from browser");
                System.err.println("✓ Add any missing authentication headers");
                System.err.println("✓ Try using a VPN or proxy server");
                System.err.println("✓ Consider browser automation approach");
                
            } else if (e.getMessage().contains("timeout")) {
                System.err.println("SUGGESTION: Request timed out - network may be slow");
            } else if (e.getMessage().contains("SSL")) {
                System.err.println("SUGGESTION: SSL/TLS issue - this client should bypass SSL issues");
            }
            
            return null;
        }
    }
    
    /**
     * 设置高级浏览器请求头
     */
    private void setupAdvancedHeaders(HttpURLConnection connection, String cookieHeader) {
        // 随机选择User-Agent
        String userAgent = userAgents.get(random.nextInt(userAgents.size()));
        
        // 基础请求头
        connection.setRequestProperty("User-Agent", userAgent);
        connection.setRequestProperty("Accept", "application/json, text/plain, */*");
        connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6");
        connection.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
        connection.setRequestProperty("Cache-Control", "no-cache");
        connection.setRequestProperty("Pragma", "no-cache");
        
        // 现代浏览器安全头
        connection.setRequestProperty("Sec-Fetch-Dest", "empty");
        connection.setRequestProperty("Sec-Fetch-Mode", "cors");
        connection.setRequestProperty("Sec-Fetch-Site", "same-origin");
        connection.setRequestProperty("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
        connection.setRequestProperty("Sec-Ch-Ua-Mobile", "?0");
        connection.setRequestProperty("Sec-Ch-Ua-Platform", "\"Windows\"");
        
        // 连接相关头
        connection.setRequestProperty("Connection", "keep-alive");
        connection.setRequestProperty("DNT", "1");
        connection.setRequestProperty("Upgrade-Insecure-Requests", "1");
        
        // 站点特定头
        connection.setRequestProperty("Origin", "https://www.ps3838.com");
        connection.setRequestProperty("Referer", "https://www.ps3838.com/zh-cn/sports/soccer");
        
        // 如果提供了Cookie，设置Cookie头
        if (cookieHeader != null && !cookieHeader.trim().isEmpty()) {
            connection.setRequestProperty("Cookie", cookieHeader);
            System.out.println("Added authentication cookie");
        }
        
        // 随机延迟模拟人类行为
        try {
            Thread.sleep(100 + random.nextInt(200)); // 100-300ms随机延迟
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 读取响应内容
     */
    private String readResponse(HttpURLConnection connection, int responseCode) throws IOException {
        BufferedReader reader;
        
        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
        }
        
        StringBuilder response = new StringBuilder();
        String line;
        
        while ((line = reader.readLine()) != null) {
            response.append(line).append("\n");
        }
        reader.close();
        
        return response.toString();
    }
    
    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        System.out.println("=== Simple Advanced HTTP Client Test ===");
        System.out.println("This client bypasses SSL issues and simulates real browser behavior");
        System.out.println();
        
        SimpleAdvancedClient client = new SimpleAdvancedClient();
        
        // 交互式测试
        try (Scanner scanner = new Scanner(System.in)) {
            
            // 先测试无认证
            System.out.println("STEP 1: Testing without authentication...");
            testBasicConnection(client);
            
            System.out.println("\n" + "=".repeat(60));
            System.out.println("STEP 2: Testing with authentication");
            System.out.println("=".repeat(60));
            
            System.out.println("\nTo get authentication data:");
            System.out.println("1. Open https://www.ps3838.com in your browser");
            System.out.println("2. Open Developer Tools (F12) -> Network tab");
            System.out.println("3. Navigate to sports section to trigger API calls");
            System.out.println("4. Find request to '/sports-service/sv/compact/events'");
            System.out.println("5. Copy the 'Cookie' header value");
            System.out.println();
            
            System.out.print("Enter Cookie header (or press Enter to skip): ");
            String cookieHeader = scanner.nextLine().trim();
            
            if (!cookieHeader.isEmpty()) {
                System.out.println("\nTesting with authentication...");
                testWithAuthentication(client, cookieHeader);
            } else {
                System.out.println("\nSkipped authentication test");
                System.out.println("For best results, please provide authentication cookies");
            }
            
        } catch (Exception e) {
            System.err.println("Error during interactive test: " + e.getMessage());
        }
        
        System.out.println("\nTest completed!");
    }
    
    /**
     * 测试基础连接
     */
    private static void testBasicConnection(SimpleAdvancedClient client) {
        String[] testUrls = {
            "https://www.ps3838.com",
            "https://www.ps3838.com/sports-service/sv/compact/events?sp=29&locale=zh_CN"
        };
        
        for (String url : testUrls) {
            System.out.println("\nTesting: " + url);
            String response = client.executeGet(url, null);
            analyzeResponse(response);
        }
    }
    
    /**
     * 测试带认证的连接
     */
    private static void testWithAuthentication(SimpleAdvancedClient client, String cookieHeader) {
        System.out.println("Using Cookie: " + maskSensitiveInfo(cookieHeader));
        
        String[] testUrls = {
            "https://www.ps3838.com/sports-service/sv/compact/events?sp=29&locale=zh_CN",
            "https://www.ps3838.com/sports-service/sv/compact/events?sp=1&locale=zh_CN"
        };
        
        for (String url : testUrls) {
            System.out.println("\nTesting with auth: " + url);
            String response = client.executeGet(url, cookieHeader);
            analyzeResponse(response);
        }
    }
    
    /**
     * 分析响应内容
     */
    private static void analyzeResponse(String response) {
        if (response == null || response.isEmpty()) {
            System.out.println("❌ No response received");
            return;
        }
        
        if (response.startsWith("{") || response.startsWith("[")) {
            System.out.println("✅ EXCELLENT: Valid JSON response!");
            
            if (response.contains("leagues") || response.contains("events") || response.contains("odds")) {
                System.out.println("✅ Sports data detected!");
            }
            
            // 显示JSON预览
            if (response.length() > 200) {
                System.out.println("JSON Preview: " + response.substring(0, 200) + "...");
            } else {
                System.out.println("Full JSON: " + response);
            }
            
        } else if (response.toLowerCase().contains("login") || response.toLowerCase().contains("signin")) {
            System.out.println("⚠️ Authentication required - response suggests login needed");
            
        } else if (response.toLowerCase().contains("access denied") || response.toLowerCase().contains("forbidden")) {
            System.out.println("❌ Access denied - may need proper authentication");
            
        } else {
            System.out.println("📄 Got response but format unclear");
            if (response.length() > 150) {
                System.out.println("Preview: " + response.substring(0, 150) + "...");
            } else {
                System.out.println("Full response: " + response);
            }
        }
    }
    
    /**
     * 隐藏敏感信息
     */
    private static String maskSensitiveInfo(String cookie) {
        if (cookie.length() > 50) {
            return cookie.substring(0, 20) + "***[MASKED]***" + cookie.substring(cookie.length() - 10);
        }
        return cookie.substring(0, Math.min(cookie.length(), 20)) + "***";
    }
}
