package com.zqsport.http;

import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.ssl.SSLContextBuilder;

import javax.net.ssl.SSLContext;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 高级HTTP客户端 - 专门解决反爬虫保护
 */
public class AdvancedHttpClient {
    
    private CloseableHttpClient httpClient;
    private Random random = new Random();
    
    // 多个User-Agent轮换使用
    private final List<String> userAgents = Arrays.asList(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    );
    
    public AdvancedHttpClient() {
        initializeClient();
    }
    
    /**
     * 初始化高级HTTP客户端
     */
    private void initializeClient() {
        try {
            // 创建信任所有证书的SSL上下文
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(new TrustAllStrategy())
                    .build();
            
            // 创建SSL连接工厂（绕过主机名验证）
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    NoopHostnameVerifier.INSTANCE
            );
            
            // 注册协议
            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder
                    .<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", sslSocketFactory)
                    .build();
            
            // 创建连接池管理器
            PoolingHttpClientConnectionManager connectionManager = 
                    new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            connectionManager.setMaxTotal(50);
            connectionManager.setDefaultMaxPerRoute(20);
            
            // 配置请求参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(org.apache.hc.core5.util.Timeout.ofSeconds(30))
                    .setResponseTimeout(org.apache.hc.core5.util.Timeout.ofSeconds(60))
                    .setConnectionRequestTimeout(org.apache.hc.core5.util.Timeout.ofSeconds(10))
                    .build();
            
            // 创建HTTP客户端
            this.httpClient = HttpClients.custom()
                    .setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(requestConfig)
                    .build();
                    
            System.out.println("Advanced HTTP Client initialized successfully");
            
        } catch (Exception e) {
            System.err.println("Failed to initialize advanced HTTP client: " + e.getMessage());
            // 降级到默认客户端
            this.httpClient = HttpClients.createDefault();
        }
    }
    
    /**
     * 执行GET请求
     */
    public String executeGet(String url) {
        return executeGet(url, null);
    }
    
    /**
     * 执行GET请求（带自定义认证信息）
     */
    public String executeGet(String url, String cookieHeader) {
        try {
            HttpGet request = new HttpGet(url);
            
            // 设置完整的浏览器请求头
            setupAdvancedHeaders(request, cookieHeader);
            
            System.out.println("Executing request to: " + url);
            
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                System.out.println("Response status: " + statusCode);
                System.out.println("Response length: " + responseBody.length() + " characters");
                
                if (statusCode >= 200 && statusCode < 300) {
                    System.out.println("SUCCESS: Request completed successfully");
                    return responseBody;
                } else {
                    System.out.println("WARNING: Unexpected status code: " + statusCode);
                    if (responseBody.length() > 0) {
                        System.out.println("Response preview: " + 
                            responseBody.substring(0, Math.min(200, responseBody.length())));
                    }
                    return responseBody; // 仍然返回响应，可能包含有用信息
                }
            }
            
        } catch (Exception e) {
            System.err.println("Request failed: " + e.getMessage());
            
            // 根据异常类型提供具体建议
            if (e.getMessage().contains("Connection reset")) {
                System.err.println("SUGGESTION: Connection was reset by server");
                System.err.println("  - Server may have anti-bot protection");
                System.err.println("  - Try adding authentication cookies");
                System.err.println("  - Consider using proxy or VPN");
            } else if (e.getMessage().contains("timeout")) {
                System.err.println("SUGGESTION: Request timed out");
                System.err.println("  - Network may be slow");
                System.err.println("  - Try increasing timeout values");
            } else if (e.getMessage().contains("SSL")) {
                System.err.println("SUGGESTION: SSL/TLS issue");
                System.err.println("  - Certificate verification problem");
                System.err.println("  - This client should bypass SSL issues");
            }
            
            return null;
        }
    }
    
    /**
     * 设置高级浏览器请求头
     */
    private void setupAdvancedHeaders(HttpGet request, String cookieHeader) {
        // 随机选择User-Agent
        String userAgent = userAgents.get(random.nextInt(userAgents.size()));
        
        // 基础请求头
        request.setHeader("User-Agent", userAgent);
        request.setHeader("Accept", "application/json, text/plain, */*");
        request.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6");
        request.setHeader("Accept-Encoding", "gzip, deflate, br");
        request.setHeader("Cache-Control", "no-cache");
        request.setHeader("Pragma", "no-cache");
        
        // 现代浏览器安全头
        request.setHeader("Sec-Fetch-Dest", "empty");
        request.setHeader("Sec-Fetch-Mode", "cors");
        request.setHeader("Sec-Fetch-Site", "same-origin");
        request.setHeader("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"");
        request.setHeader("Sec-Ch-Ua-Mobile", "?0");
        request.setHeader("Sec-Ch-Ua-Platform", "\"Windows\"");
        
        // 连接相关头
        request.setHeader("Connection", "keep-alive");
        request.setHeader("DNT", "1");
        request.setHeader("Upgrade-Insecure-Requests", "1");
        
        // 站点特定头
        request.setHeader("Origin", "https://www.ps3838.com");
        request.setHeader("Referer", "https://www.ps3838.com/zh-cn/sports/soccer");
        
        // 如果提供了Cookie，设置Cookie头
        if (cookieHeader != null && !cookieHeader.trim().isEmpty()) {
            request.setHeader("Cookie", cookieHeader);
            System.out.println("Added custom cookie header");
        }
        
        // 随机延迟模拟人类行为
        try {
            Thread.sleep(100 + random.nextInt(200)); // 100-300ms随机延迟
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 测试连接
     */
    public boolean testConnection(String url) {
        try {
            String response = executeGet(url);
            return response != null && !response.isEmpty();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 关闭客户端
     */
    public void close() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (Exception e) {
            System.err.println("Error closing HTTP client: " + e.getMessage());
        }
    }
    
    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        System.out.println("=== Advanced HTTP Client Test ===");
        
        AdvancedHttpClient client = new AdvancedHttpClient();
        
        // 测试URLs
        String[] testUrls = {
            "https://httpbin.org/headers",  // 测试请求头
            "https://www.ps3838.com",       // 目标网站首页
            "https://www.ps3838.com/sports-service/sv/compact/events?sp=29&locale=zh_CN" // API端点
        };
        
        for (String url : testUrls) {
            System.out.println("\n" + "=".repeat(60));
            System.out.println("Testing URL: " + url);
            System.out.println("=".repeat(60));
            
            String response = client.executeGet(url);
            
            if (response != null) {
                if (response.length() > 200) {
                    System.out.println("Response preview: " + response.substring(0, 200) + "...");
                } else {
                    System.out.println("Full response: " + response);
                }
            }
            
            // 短暂休息
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        client.close();
        System.out.println("\nTest completed!");
    }
}
