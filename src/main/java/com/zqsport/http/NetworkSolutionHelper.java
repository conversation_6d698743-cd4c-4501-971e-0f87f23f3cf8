package com.zqsport.http;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URL;
import java.util.Arrays;
import java.util.List;

/**
 * 网络连接问题解决方案助手
 * 提供多种解决网络连接失败的方法
 */
public class NetworkSolutionHelper {
    
    private static final Logger logger = LoggerFactory.getLogger(NetworkSolutionHelper.class);
    
    /**
     * 诊断并提供解决方案
     */
    public static void diagnoseAndSuggestSolutions() {
        logger.info("🔍 开始网络连接问题诊断...");
        
        // 1. 基本网络连通性检查
        boolean basicConnectivity = checkBasicConnectivity();
        
        // 2. 目标网站可达性检查
        boolean targetReachable = checkTargetWebsite();
        
        // 3. DNS解析检查
        boolean dnsWorking = checkDNSResolution();
        
        // 4. 提供解决方案
        provideSolutions(basicConnectivity, targetReachable, dnsWorking);
    }
    
    /**
     * 检查基本网络连通性
     */
    private static boolean checkBasicConnectivity() {
        logger.info("📡 检查基本网络连通性...");
        
        String[] testUrls = {
            "https://www.baidu.com",
            "https://www.bing.com",
            "https://httpbin.org/get"
        };
        
        for (String url : testUrls) {
            try {
                HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
                connection.setRequestMethod("HEAD");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                
                int responseCode = connection.getResponseCode();
                if (responseCode == 200) {
                    logger.info("✅ 基本网络连接正常 (测试URL: {})", url);
                    return true;
                }
            } catch (Exception e) {
                logger.debug("测试URL失败: {} - {}", url, e.getMessage());
            }
        }
        
        logger.warn("❌ 基本网络连接异常");
        return false;
    }
    
    /**
     * 检查目标网站可达性
     */
    private static boolean checkTargetWebsite() {
        logger.info("🎯 检查目标网站可达性...");
        
        try {
            HttpURLConnection connection = (HttpURLConnection) 
                new URL("https://www.ps3838.com").openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            // 添加浏览器头部
            connection.setRequestProperty("User-Agent", HttpConfig.USER_AGENT);
            connection.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
            
            int responseCode = connection.getResponseCode();
            logger.info("目标网站响应码: {}", responseCode);
            
            return responseCode >= 200 && responseCode < 400;
            
        } catch (Exception e) {
            logger.warn("❌ 目标网站不可达: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查DNS解析
     */
    private static boolean checkDNSResolution() {
        logger.info("🌐 检查DNS解析...");
        
        try {
            java.net.InetAddress address = java.net.InetAddress.getByName("www.ps3838.com");
            logger.info("✅ DNS解析成功: {} -> {}", "www.ps3838.com", address.getHostAddress());
            return true;
        } catch (Exception e) {
            logger.warn("❌ DNS解析失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 提供解决方案
     */
    private static void provideSolutions(boolean basicConnectivity, boolean targetReachable, boolean dnsWorking) {
        logger.info("\n" + "=".repeat(80));
        logger.info("💡 网络连接问题解决方案:");
        logger.info("=".repeat(80));
        
        if (!basicConnectivity) {
            logger.error("🚨 基本网络连接问题:");
            logger.error("  1. 检查网络连接是否正常");
            logger.error("  2. 检查防火墙设置");
            logger.error("  3. 检查代理设置");
            logger.error("  4. 重启网络适配器");
        }
        
        if (!dnsWorking) {
            logger.error("🚨 DNS解析问题:");
            logger.error("  1. 更换DNS服务器 (*******, ***************)");
            logger.error("  2. 清除DNS缓存: ipconfig /flushdns");
            logger.error("  3. 检查hosts文件");
        }
        
        if (basicConnectivity && !targetReachable) {
            logger.warn("⚠️ 目标网站访问受限:");
            logger.warn("  1. 网站可能有地区限制");
            logger.warn("  2. 使用VPN或代理服务器");
            logger.warn("  3. 检查是否需要特殊认证");
            logger.warn("  4. 尝试使用备用API端点");
            logger.warn("  5. 联系网站管理员确认API状态");
        }
        
        // 提供具体的解决步骤
        logger.info("\n📋 推荐解决步骤:");
        logger.info("1. 运行网络诊断: java -cp \"target/classes;target/dependency/*\" com.zqsport.http.NetworkSolutionHelper");
        logger.info("2. 尝试使用代理: 修改HttpConfig中的代理设置");
        logger.info("3. 使用备用数据源: 切换到其他体育数据API");
        logger.info("4. 检查防火墙: 确保Java程序可以访问网络");
        
        logger.info("=".repeat(80));
    }
    
    /**
     * 测试代理连接
     */
    public static boolean testProxyConnection(String proxyHost, int proxyPort) {
        logger.info("🔄 测试代理连接: {}:{}", proxyHost, proxyPort);
        
        try {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));
            HttpURLConnection connection = (HttpURLConnection) 
                new URL("https://httpbin.org/ip").openConnection(proxy);
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream()))) {
                    String response = reader.readLine();
                    logger.info("✅ 代理连接成功，IP信息: {}", response);
                    return true;
                }
            }
        } catch (Exception e) {
            logger.warn("❌ 代理连接失败: {}", e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 主方法 - 运行诊断
     */
    public static void main(String[] args) {
        logger.info("🚀 启动网络连接问题解决助手...");
        
        diagnoseAndSuggestSolutions();
        
        // 如果配置了代理，测试代理连接
        if (HttpConfig.USE_PROXY) {
            testProxyConnection(HttpConfig.PROXY_HOST, HttpConfig.PROXY_PORT);
        }
        
        logger.info("✅ 诊断完成！");
    }
}
