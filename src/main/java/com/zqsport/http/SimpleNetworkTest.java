package com.zqsport.http;

import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.io.entity.EntityUtils;

/**
 * 简单网络测试（不依赖复杂的日志框架）
 */
public class SimpleNetworkTest {
    
    public static void main(String[] args) {
        System.out.println("🌐 开始简单网络连接测试...");
        
        // 测试目标URL
        String[] testUrls = {
            "https://www.baidu.com",
            "https://www.google.com", 
            "https://www.ps3838.com",
            "https://www.ps3838.com/sports-service/sv/compact/events?sp=29&locale=zh_CN"
        };
        
        for (String url : testUrls) {
            System.out.println("\n📍 测试URL: " + url);
            testConnection(url);
        }
        
        System.out.println("\n🏁 测试完成！");
    }
    
    private static void testConnection(String url) {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            
            // 设置基本请求头
            request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            request.setHeader("Accept", "application/json, text/plain, */*");
            request.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            request.setHeader("Cache-Control", "no-cache");
            
            System.out.print("   连接中... ");
            
            try (CloseableHttpResponse response = client.execute(request)) {
                int statusCode = response.getCode();
                String responseBody = EntityUtils.toString(response.getEntity());
                
                if (statusCode >= 200 && statusCode < 300) {
                    System.out.println("✅ 成功 (状态码: " + statusCode + ")");
                    System.out.println("   响应长度: " + responseBody.length() + " 字符");
                    
                    if (responseBody.contains("{") || responseBody.contains("[")) {
                        System.out.println("   ✅ 响应包含JSON数据");
                    } else if (responseBody.contains("html")) {
                        System.out.println("   📄 响应是HTML页面");
                    }
                    
                    // 显示响应预览
                    if (responseBody.length() > 100) {
                        System.out.println("   预览: " + responseBody.substring(0, 100) + "...");
                    } else {
                        System.out.println("   内容: " + responseBody);
                    }
                    
                } else {
                    System.out.println("⚠️ 警告 (状态码: " + statusCode + ")");
                    System.out.println("   响应: " + responseBody.substring(0, Math.min(200, responseBody.length())));
                }
                
            }
            
        } catch (Exception e) {
            System.out.println("❌ 失败: " + e.getMessage());
            
            // 显示更详细的错误信息
            if (e.getMessage().contains("ConnectException")) {
                System.out.println("   💡 网络连接失败，请检查网络或防火墙设置");
            } else if (e.getMessage().contains("SSLException")) {
                System.out.println("   💡 SSL证书问题，可能需要宽松SSL配置");
            } else if (e.getMessage().contains("UnknownHostException")) {
                System.out.println("   💡 域名解析失败，请检查DNS设置");
            } else if (e.getMessage().contains("SocketTimeoutException")) {
                System.out.println("   💡 连接超时，网络可能较慢");
            }
        }
    }
}
