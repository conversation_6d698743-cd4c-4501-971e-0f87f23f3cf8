package com.zqsport.http;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zqsport.http.model.FootballData;
import com.zqsport.http.parser.FootballDataParser;
import com.zqsport.util.NetworkDiagnostics;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Sports HTTP Client for getting sports betting data via HTTP requests
 */
public class SportsHttpClient {

    private static final Logger logger = LoggerFactory.getLogger(SportsHttpClient.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final FootballDataParser footballParser = new FootballDataParser();
    private CloseableHttpClient httpClient;
    private String token;
    private String ulp;

    public SportsHttpClient() {
        initializeHttpClient();
    }

    // 移除MessageProcessor构造函数

    /**
     * 初始化HTTP客户端 - 增强版本，包含连接池和宽松SSL配置
     */
    private void initializeHttpClient() {
        try {
            // 尝试使用宽松SSL配置
            this.httpClient = com.zqsport.util.NetworkDiagnostics.createRelaxedSslClient();
            logger.info("HTTP客户端已初始化（宽松SSL配置）");

        } catch (Exception e) {
            logger.error("初始化宽松SSL客户端失败，使用标准配置", e);

            try {
                // 标准配置
                PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
                connectionManager.setMaxTotal(20);
                connectionManager.setDefaultMaxPerRoute(10);
                connectionManager.setValidateAfterInactivity(org.apache.hc.core5.util.TimeValue.ofMilliseconds(30000));

                RequestConfig config = RequestConfig.custom()
                        .setConnectionRequestTimeout(org.apache.hc.core5.util.Timeout.ofMilliseconds(HttpConfig.CONNECTION_TIMEOUT))
                        .setResponseTimeout(org.apache.hc.core5.util.Timeout.ofMilliseconds(HttpConfig.REQUEST_TIMEOUT))
                        .setConnectTimeout(org.apache.hc.core5.util.Timeout.ofMilliseconds(HttpConfig.CONNECTION_TIMEOUT))
                        .build();

                this.httpClient = HttpClients.custom()
                        .setConnectionManager(connectionManager)
                        .setDefaultRequestConfig(config)
                        .build();

                logger.info("HTTP客户端已初始化（标准配置）");

            } catch (Exception e2) {
                logger.error("初始化标准HTTP客户端也失败，使用默认配置", e2);
                this.httpClient = HttpClients.createDefault();
                logger.info("HTTP客户端已初始化（默认配置）");
            }
        }
    }

    /**
     * 设置认证参数
     */
    public void setCredentials(String token, String ulp) {
        this.token = token;
        this.ulp = ulp;
        logger.info("HTTP客户端认证参数已设置");
    }

    // 移除MessageProcessor相关方法

    /**
     * 获取赔率数据 - 使用GET请求（基于真实请求格式）- 增强版本
     */
    public String getOddsData(int sportId, String locale) {
        return getOddsDataWithRetry(sportId, locale, 3, 1000);
    }

    /**
     * 带重试机制的获取赔率数据方法
     */
    private String getOddsDataWithRetry(int sportId, String locale, int maxRetries, long baseDelayMs) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                String queryParams = HttpConfig.buildQueryParams(sportId, locale);
                String url = HttpConfig.buildOddsApiUrl() + "?" + queryParams;

                HttpGet request = new HttpGet(url);
                setupRequestHeaders(request);

                if (attempt == 1) {
                    logger.info("发送GET请求获取赔率数据: {}", url);
                } else {
                    logger.info("重试第{}次请求获取赔率数据", attempt);
                }

                try (CloseableHttpResponse response = httpClient.execute(request)) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    int statusCode = response.getCode();

                    logger.info("HTTP响应状态码: {}", statusCode);

                    if (statusCode == 200) {
                        logger.info("✅ 成功获取赔率数据，响应长度: {}", responseBody.length());
                        processOddsResponse(responseBody);
                        return responseBody;
                    } else if (statusCode == 429) {
                        // 请求过于频繁，需要等待更长时间
                        logger.warn("⚠️ 请求频率过高 (429)，等待更长时间后重试...");
                        if (attempt < maxRetries) {
                            Thread.sleep(baseDelayMs * 5); // 等待5倍时间
                        }
                        lastException = new IOException("HTTP 429 - Too Many Requests");
                        continue;
                    } else if (statusCode >= 500) {
                        // 服务器错误，可以重试
                        logger.warn("⚠️ 服务器错误 ({}), 准备重试...", statusCode);
                        lastException = new IOException("HTTP " + statusCode + " - Server Error");
                        if (attempt < maxRetries) {
                            Thread.sleep(baseDelayMs * attempt); // 指数退避
                        }
                        continue;
                    } else {
                        // 其他HTTP错误，不重试
                        logger.warn("❌ HTTP请求失败，状态码: {}, 响应: {}", statusCode,
                                responseBody.length() > 200 ? responseBody.substring(0, 200) + "..." : responseBody);
                        return null;
                    }
                }

            } catch (SocketException | SocketTimeoutException e) {
                // 网络连接问题，可以重试
                lastException = e;
                logger.warn("⚠️ 网络连接问题 (尝试 {}/{}): {}", attempt, maxRetries, e.getMessage());

                if (attempt < maxRetries) {
                    try {
                        long delay = baseDelayMs * (long) Math.pow(2, attempt - 1); // 指数退避
                        logger.info("等待 {}ms 后重试...", delay);
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        logger.error("重试等待被中断", ie);
                        break;
                    }
                }
            } catch (IOException e) {
                // 其他IO异常，可以重试
                lastException = e;
                logger.warn("⚠️ IO异常 (尝试 {}/{}): {}", attempt, maxRetries, e.getMessage());

                if (attempt < maxRetries) {
                    try {
                        long delay = baseDelayMs * attempt;
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } catch (Exception e) {
                // 其他异常，不重试
                logger.error("❌ 获取赔率数据时发生未预期的错误", e);
                return null;
            }
        }

        // 所有重试都失败了
        logger.error("❌ 经过{}次重试后仍然失败，最后一个错误: {}", maxRetries,
                lastException != null ? lastException.getMessage() : "未知错误");
        return null;
    }

    // 移除未使用的备用端点方法

    /**
     * 设置HTTP请求头（增强版本，模拟真实浏览器）
     */
    private void setupRequestHeaders(org.apache.hc.core5.http.HttpRequest request) {
        // 基本请求头 - 模拟真实浏览器
        request.setHeader("User-Agent", HttpConfig.USER_AGENT);
        request.setHeader("Accept", "application/json, text/plain, */*");
        request.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6");
        request.setHeader("Accept-Encoding", "gzip, deflate, br");
        request.setHeader("Cache-Control", "no-cache");
        request.setHeader("Pragma", "no-cache");
        request.setHeader("Referer", HttpConfig.REFERER);
        request.setHeader("Origin", HttpConfig.ORIGIN);

        // 添加更多浏览器特征头
        request.setHeader("Sec-Fetch-Dest", "empty");
        request.setHeader("Sec-Fetch-Mode", "cors");
        request.setHeader("Sec-Fetch-Site", "same-origin");
        request.setHeader("Sec-Ch-Ua", "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Microsoft Edge\";v=\"120\"");
        request.setHeader("Sec-Ch-Ua-Mobile", "?0");
        request.setHeader("Sec-Ch-Ua-Platform", "\"Windows\"");
        request.setHeader("DNT", "1"); // Do Not Track
        request.setHeader("Connection", "keep-alive");
        
        // 现代浏览器安全头
        request.setHeader("Sec-Ch-Ua", "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"");
        request.setHeader("Sec-Ch-Ua-Mobile", "?0");
        request.setHeader("Sec-Ch-Ua-Platform", "\"Windows\"");
        request.setHeader("Sec-Fetch-Dest", "empty");
        request.setHeader("Sec-Fetch-Mode", "cors");
        request.setHeader("Sec-Fetch-Site", "same-origin");
        
        // 需要根据实际登录状态设置的头（这里使用示例值）
        // 在实际使用中，这些值应该从登录后的session中获取
        if (token != null && !token.isEmpty()) {
            // 这些是从真实请求中提取的关键头信息
            // 需要根据实际登录状态动态设置
            request.setHeader("X-Browser-Session-Id", "f1f0e0c6-f678-4c3d-8cca-b226a277e782");
            request.setHeader("X-Custid", "id=ALONG7788&login=202508120839&roundTrip=202508120839&hash=2AF2D3A2E67545F124BA1530C46A3BEB");
            request.setHeader("X-Lcu", "AAAAAwAAAAAEbaPgAAABmJ6Bh8T8a6KnWj3eCCnI0R2n4Yfsi1MeBWt8vFeSz_FNFyAGpg==");
            request.setHeader("X-Slid", "1238680191");
            request.setHeader("X-U", "AAAAAwAAAAAEbaPgAAABmJ6Bh8T8a6KnWj3eCCnI0R2n4Yfsi1MeBWt8vFeSz_FNFyAGpg==");
            
            // X-App-Data头包含重要的认证信息
            String xAppData = "pctag=11021a1b-8443-4226-917f-42bc6eb20ce3;" +
                            "directusToken=TwEdnphtyxsfMpXoJkCkWaPsL2KJJ3lo;" +
                            "BrowserSessionId=f1f0e0c6-f678-4c3d-8cca-b226a277e782;" +
                            "PCTR=1912768745779;" +
                            "_og=QQ%3D%3D;" +
                            "_ulp=" + (ulp != null ? java.net.URLEncoder.encode(ulp, java.nio.charset.StandardCharsets.UTF_8) : "") + ";" +
                            "custid=id%3DALONG7788%26login%3D202508120839%26roundTrip%3D202508120839%26hash%3D2AF2D3A2E67545F124BA1530C46A3BEB;" +
                            "_userDefaultView=COMPACT;" +
                            "__prefs=W251bGwsMiwxLDAsMCxudWxsLGZhbHNlLDAuMDAwMCxmYWxzZSx0cnVlLCJBTEwiLDEsbnVsbCx0cnVlLHRydWUsZmFsc2UsZmFsc2UsbnVsbCxudWxsLGZhbHNlXQ%3D%3D;" +
                            "lang=zh_CN;" +
                            "Xg0V3=0Z8";
            request.setHeader("X-App-Data", xAppData);
            
            // Cookie头包含会话信息
            String cookieHeader = "pctag=11021a1b-8443-4226-917f-42bc6eb20ce3; " +
                                "skin=ps3838; " +
                                "_gid=GA1.2.1021602226.1755002344; " +
                                "_gat=1; " +
                                "_ga=GA1.1.794023142.1755002344; " +
                                "PCTR=1912768745779; " +
                                "u=AAAAAwAAAAAEbaPgAAABmJ6Bh8T8a6KnWj3eCCnI0R2n4Yfsi1MeBWt8vFeSz_FNFyAGpg==; " +
                                "lcu=AAAAAwAAAAAEbaPgAAABmJ6Bh8T8a6KnWj3eCCnI0R2n4Yfsi1MeBWt8vFeSz_FNFyAGpg==; " +
                                "custid=id=ALONG7788&login=202508120839&roundTrip=202508120839&hash=2AF2D3A2E67545F124BA1530C46A3BEB; " +
                                "BrowserSessionId=f1f0e0c6-f678-4c3d-8cca-b226a277e782; " +
                                "_og=QQ==; " +
                                "_ulp=" + (ulp != null ? ulp : "") + "; " +
                                "uoc=be97830afef253f33d2a502d243b8c37; " +
                                "_userDefaultView=COMPACT; " +
                                "SLID=1238680191; " +
                                "_ga_5PLZ6DPTZ0=GS2.1.s1755002345$o1$g0$t1755002346$j59$l0$h0; " +
                                "_vid=ebc1f6b6da8e3935faa991582b086534; " +
                                "__prefs=W251bGwsMiwxLDAsMCxudWxsLGZhbHNlLDAuMDAwMCxmYWxzZSx0cnVlLCJBTEwiLDEsbnVsbCx0cnVlLHRydWUsZmFsc2UsZmFsc2UsbnVsbCxudWxsLGZhbHNlXQ==; " +
                                "_lastView=eyJhbG9uZzc3ODgiOiJDT01QQUNUIn0%3D; " +
                                "Xg0V3=0Z8; " +
                                "adformfrpid=4716855459387233000; " +
                                "_sig=Mcy1NbU14TUdKaVpHVTNOakZoWldObE5ROnpqWnljWENkbnBnb0k5RlNPMjRaMTBkMmc6LTQ0NzA4MjU0Ojc1NTAwMjM1MjoyLjEwLjA6OUNDRUE5b0lYRA%3D%3D; " +
                                "_apt=9CCEA9oIXD; " +
                                "lang=zh_CN";
            request.setHeader("Cookie", cookieHeader);
        } else {
            logger.warn("未设置认证token，请求可能失败");
        }
    }



    /**
     * 处理赔率响应数据
     */
    private void processOddsResponse(String responseBody) {
        if (responseBody == null || responseBody.isEmpty()) {
            return;
        }

        try {
            // 简单的数据处理逻辑
            if (responseBody.trim().startsWith("{")) {
                // JSON格式响应
                logger.info("📈 收到JSON格式赔率数据: {}", 
                        responseBody.length() > 100 ? responseBody.substring(0, 100) + "..." : responseBody);
                
                // 可以在这里添加JSON解析逻辑
                parseJsonResponse(responseBody);
            } else {
                // 可能是HTML或其他格式
                logger.info("📄 收到非JSON格式响应，长度: {}", responseBody.length());
                // 这里可以实现HTML解析或数据提取逻辑
            }
        } catch (Exception e) {
            logger.error("处理赔率响应数据失败", e);
        }
    }
    
    /**
     * 解析JSON响应数据
     */
    private void parseJsonResponse(String jsonResponse) {
        try {
            // 首先尝试解析为标准API响应格式
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = objectMapper.readValue(jsonResponse, Map.class);
            
            // 检查是否包含联赛数据
            if (responseMap.containsKey("leagues") || responseMap.containsKey("events")) {
                parseStandardApiResponse(responseMap);
            } else {
                // 尝试解析为足球联赛数组格式
                tryParseFootballLeagueArray(jsonResponse);
            }
            
        } catch (Exception e) {
            logger.debug("标准JSON解析失败，尝试数组格式解析");
            tryParseFootballLeagueArray(jsonResponse);
        }
    }
    
    /**
     * 解析标准API响应格式
     */
    private void parseStandardApiResponse(Map<String, Object> responseMap) {
        // 提取基本信息
        if (responseMap.containsKey("events")) {
            Object events = responseMap.get("events");
            if (events instanceof java.util.List) {
                java.util.List<?> eventsList = (java.util.List<?>) events;
                logger.info("✅ 解析到 {} 个赛事", eventsList.size());
            }
        }
        
        if (responseMap.containsKey("leagues")) {
            Object leagues = responseMap.get("leagues");
            if (leagues instanceof java.util.List) {
                java.util.List<?> leaguesList = (java.util.List<?>) leagues;
                logger.info("✅ 解析到 {} 个联赛", leaguesList.size());
            }
        }
        
        if (responseMap.containsKey("total")) {
            logger.info("📊 总数据条数: {}", responseMap.get("total"));
        }
    }
    
    /**
     * 尝试解析足球联赛数组格式
     */
    private void tryParseFootballLeagueArray(String jsonResponse) {
        try {
            // 检查是否是联赛数组格式 [leagueId, leagueName, [matches...]]
            if (jsonResponse.trim().startsWith("[")) {
                logger.info("🏆 检测到足球联赛数据格式，开始解析...");
                
                FootballData.League league = footballParser.parseLeagueData(jsonResponse);
                if (league != null) {
                    logger.info("✅ 足球数据解析成功！");
                    footballParser.printLeagueData(league);
                } else {
                    logger.warn("⚠️ 足球数据解析失败");
                }
            } else {
                logger.info("📄 接收到未知格式的响应数据");
            }
            
        } catch (Exception e) {
            logger.error("足球数据解析失败", e);
        }
    }

    /**
     * 关闭HTTP客户端
     */
    public void close() {
        if (httpClient != null) {
            try {
                httpClient.close();
                logger.info("HTTP客户端已关闭");
            } catch (IOException e) {
                logger.error("关闭HTTP客户端失败", e);
            }
        }
    }

    /**
     * 测试方法 - 基于真实API端点
     */
    public static void main(String[] args) {
        SportsHttpClient client = new SportsHttpClient();
        
        try {
            // 设置测试参数（使用实际的认证信息）
            String token = "test_token"; // 需要实际的token
            String ulp = "R3RBSHZaT1hOdUI4MnRTcnlHTzhsZnV0eFUwMHFqdml6VEFsTDVVRXd1TDl5N0ZSWkU2OFNFMkwzT2VnQWJuOWxZa3czc0hobXN6TE4rejZBaVIwaXc9PXxkN2Y3YWRhNGE0NjQ0ZWZhMjI3OTBhMjg0YTdiZTYxZQ==";
            
            client.setCredentials(token, ulp);
            
            // 获取赔率数据
            logger.info("开始测试HTTP赔率数据获取...");
            String response = client.getOddsData(29, "zh_CN");
            
            if (response != null) {
                logger.info("测试成功！响应长度: {}", response.length());
                logger.info("响应内容预览: {}", response.length() > 500 ? response.substring(0, 500) + "..." : response);
            } else {
                logger.warn("测试失败，未获取到数据");
            }
            
        } catch (Exception e) {
            logger.error("测试失败", e);
        } finally {
            client.close();
        }
    }
}

