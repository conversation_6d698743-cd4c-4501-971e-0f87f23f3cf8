package com.zqsport.http;

// 移除WebSocket依赖
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Manager class for handling sports HTTP requests with polling
 */
public class SportsHttpManager {

    private static final Logger logger = LoggerFactory.getLogger(SportsHttpManager.class);
    private SportsHttpClient httpClient;
    private ScheduledExecutorService scheduler;
    private ScheduledFuture<?> pollingTask;
    
    // 配置参数
    private int pollingInterval = HttpConfig.DEFAULT_POLLING_INTERVAL; // 默认5秒
    private AtomicBoolean isRunning = new AtomicBoolean(false);
    
    // 请求参数
    private int currentSportId = HttpConfig.RequestParams.SPORT_ID;
    private String currentLocale = HttpConfig.RequestParams.LOCALE;
    
    // 统计信息
    private long totalRequests = 0;
    private long successfulRequests = 0;
    private long failedRequests = 0;
    private long lastSuccessTime = 0;
    private long consecutiveFailures = 0;
    private long startTime = System.currentTimeMillis();

    public SportsHttpManager() {
        this.httpClient = new SportsHttpClient();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "SportsHttpPolling");
            t.setDaemon(true);
            return t;
        });
    }

    // 移除MessageProcessor构造函数

    // 移除MessageProcessor相关方法

    /**
     * 设置轮询间隔
     */
    public void setPollingInterval(int intervalMs) {
        if (intervalMs < HttpConfig.MIN_POLLING_INTERVAL) {
            intervalMs = HttpConfig.MIN_POLLING_INTERVAL;
            logger.warn("轮询间隔过小，已调整为最小值: {}ms", intervalMs);
        } else if (intervalMs > HttpConfig.MAX_POLLING_INTERVAL) {
            intervalMs = HttpConfig.MAX_POLLING_INTERVAL;
            logger.warn("轮询间隔过大，已调整为最大值: {}ms", intervalMs);
        }
        this.pollingInterval = intervalMs;
        logger.info("轮询间隔已设置为: {}ms", intervalMs);
    }

    /**
     * 直接连接并开始轮询（不需要Token认证）
     */
    public boolean connect() {
        return connect(null, null, true);
    }

    /**
     * 连接并开始轮询
     */
    public boolean connect(String token, String ulp) {
        return connect(token, ulp, true);
    }

    /**
     * 连接并可选择是否开始轮询
     */
    public boolean connect(String token, String ulp, boolean startPolling) {
        try {
            // 设置HTTP客户端认证参数（可选）
            if (token != null && ulp != null) {
                httpClient.setCredentials(token, ulp);
                logger.info("HTTP客户端认证参数已设置");
            } else {
                logger.info("HTTP客户端无认证模式启动");
            }
            
            logger.info("HTTP客户端连接配置完成");
            
            if (startPolling) {
                return startPolling();
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("连接失败", e);
            return false;
        }
    }

    /**
     * 开始轮询
     */
    public boolean startPolling() {
        if (isRunning.get()) {
            logger.warn("轮询已在运行中");
            return true;
        }

        try {
            logger.info("开始HTTP轮询，间隔: {}ms", pollingInterval);
            
            // 立即执行一次请求
            performOddsRequest();
            
            // 启动定时轮询任务
            pollingTask = scheduler.scheduleAtFixedRate(
                this::performOddsRequest,
                pollingInterval,
                pollingInterval,
                TimeUnit.MILLISECONDS
            );
            
            isRunning.set(true);
            logger.info("✅ HTTP轮询已启动");
            return true;
            
        } catch (Exception e) {
            logger.error("启动轮询失败", e);
            return false;
        }
    }

    /**
     * 停止轮询
     */
    public void stopPolling() {
        if (!isRunning.get()) {
            logger.info("轮询未在运行");
            return;
        }

        if (pollingTask != null && !pollingTask.isCancelled()) {
            pollingTask.cancel(false);
            pollingTask = null;
        }

        isRunning.set(false);
        logger.info("❌ HTTP轮询已停止");
    }

    /**
     * 执行赔率请求 - 增强版本
     */
    private void performOddsRequest() {
        try {
            totalRequests++;
            logger.debug("执行第 {} 次赔率请求 (sport: {}, locale: {})",
                    totalRequests, currentSportId, currentLocale);

            String response = httpClient.getOddsData(currentSportId, currentLocale);

            if (response != null && !response.isEmpty()) {
                successfulRequests++;
                lastSuccessTime = System.currentTimeMillis();
                consecutiveFailures = 0; // 重置连续失败计数

                logger.debug("✅ 请求成功 ({}/{})", successfulRequests, totalRequests);

                // 每100次成功请求打印一次统计信息
                if (successfulRequests % 100 == 0) {
                    printStatistics();
                }
            } else {
                failedRequests++;
                consecutiveFailures++;

                logger.warn("❌ 请求失败 ({}/{}) - 连续失败: {}",
                        failedRequests, totalRequests, consecutiveFailures);

                // 连续失败处理
                handleRequestFailure();
            }

        } catch (Exception e) {
            failedRequests++;
            consecutiveFailures++;
            logger.error("执行赔率请求时出错 ({}/{}) - 连续失败: {}",
                    failedRequests, totalRequests, consecutiveFailures, e);

            handleRequestFailure();
        }
    }

    /**
     * 处理请求失败 - 增强版本
     */
    private void handleRequestFailure() {
        if (consecutiveFailures >= 10) {
            logger.error("⚠️ 连续失败{}次，可能存在严重网络问题", consecutiveFailures);

            // 如果连续失败超过20次，暂时停止轮询
            if (consecutiveFailures >= 20) {
                logger.error("🛑 连续失败过多，暂停轮询60秒...");
                stopPolling();

                // 60秒后自动重启
                scheduler.schedule(() -> {
                    logger.info("🔄 自动重启轮询...");
                    startPolling();
                }, 60, TimeUnit.SECONDS);
            }
        } else if (consecutiveFailures % 5 == 0) {
            logger.warn("⚠️ 连续失败{}次，检查网络连接状态...", consecutiveFailures);

            // 可以在这里添加网络诊断逻辑
            performNetworkDiagnostics();
        }
    }

    /**
     * 执行网络诊断
     */
    private void performNetworkDiagnostics() {
        try {
            // 简单的网络连通性检查
            java.net.URL url = new java.net.URL("https://www.google.com");
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                logger.info("🌐 网络连接正常，问题可能在目标服务器");
            } else {
                logger.warn("🌐 网络连接异常，响应码: {}", responseCode);
            }
            connection.disconnect();

        } catch (Exception e) {
            logger.warn("🌐 网络诊断失败: {}", e.getMessage());
        }
    }

    /**
     * 订阅赔率数据（设置请求参数）
     */
    public void subscribeToOdds(int sportId, String locale) {
        this.currentSportId = sportId;
        this.currentLocale = locale;
        logger.info("📊 订阅参数已设置: sportId={}, locale={}", sportId, locale);
        
        // 如果正在轮询，立即执行一次请求
        if (isRunning.get()) {
            scheduler.execute(this::performOddsRequest);
        }
    }

    /**
     * 使用默认参数订阅赔率数据
     */
    public void subscribeToOdds() {
        subscribeToOdds(HttpConfig.RequestParams.SPORT_ID, HttpConfig.RequestParams.LOCALE);
    }

    /**
     * 手动执行一次请求
     */
    public String fetchOddsNow() {
        try {
            logger.info("手动执行赔率请求...");
            return httpClient.getOddsData(currentSportId, currentLocale);
        } catch (Exception e) {
            logger.error("手动执行请求失败", e);
            return null;
        }
    }

    /**
     * 断开连接并停止轮询
     */
    public void disconnect() {
        logger.info("断开HTTP连接...");
        
        stopPolling();
        
        if (httpClient != null) {
            httpClient.close();
        }
        
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        logger.info("✅ HTTP连接已断开");
    }

    /**
     * 检查是否在轮询中
     */
    public boolean isConnected() {
        return isRunning.get();
    }

    /**
     * 检查是否正在轮询
     */
    public boolean isPolling() {
        return isRunning.get();
    }

    /**
     * 获取轮询间隔
     */
    public int getPollingInterval() {
        return pollingInterval;
    }

    /**
     * 打印详细统计信息
     */
    public void printStatistics() {
        long currentTime = System.currentTimeMillis();
        long runningTime = currentTime - startTime;
        long runningMinutes = runningTime / (1000 * 60);

        double successRate = totalRequests > 0 ? (double) successfulRequests / totalRequests * 100 : 0;
        long timeSinceLastSuccess = lastSuccessTime > 0 ? (currentTime - lastSuccessTime) / 1000 : -1;

        logger.info("📊 HTTP轮询统计信息:");
        logger.info("  运行时间: {} 分钟", runningMinutes);
        logger.info("  总请求数: {}", totalRequests);
        logger.info("  成功请求: {} ({:.1f}%)", successfulRequests, successRate);
        logger.info("  失败请求: {}", failedRequests);
        logger.info("  连续失败: {}", consecutiveFailures);
        logger.info("  轮询间隔: {}ms", pollingInterval);
        logger.info("  当前状态: {}", isRunning.get() ? "运行中" : "已停止");

        if (timeSinceLastSuccess >= 0) {
            logger.info("  距离上次成功: {}秒", timeSinceLastSuccess);
        } else {
            logger.info("  距离上次成功: 从未成功");
        }

        // 性能指标
        if (runningMinutes > 0) {
            double requestsPerMinute = (double) totalRequests / runningMinutes;
            logger.info("  平均请求频率: {:.1f} 请求/分钟", requestsPerMinute);
        }
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalRequests = 0;
        successfulRequests = 0;
        failedRequests = 0;
        consecutiveFailures = 0;
        lastSuccessTime = 0;
        startTime = System.currentTimeMillis();
        logger.info("📊 统计信息已重置");
    }

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("totalRequests", totalRequests);
        stats.put("successfulRequests", successfulRequests);
        stats.put("failedRequests", failedRequests);
        stats.put("successRate", totalRequests > 0 ? (double) successfulRequests / totalRequests * 100 : 0);
        stats.put("lastSuccessTime", lastSuccessTime);
        stats.put("isRunning", isRunning.get());
        stats.put("pollingInterval", pollingInterval);
        stats.put("currentSportId", currentSportId);
        stats.put("currentLocale", currentLocale);
        return stats;
    }


}

