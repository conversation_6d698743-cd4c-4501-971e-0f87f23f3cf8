package com.zqsport.http;

/**
 * 浏览器认证信息获取助手
 * 帮助用户从浏览器中获取必要的认证信息
 */
public class BrowserAuthHelper {
    
    public static void main(String[] args) {
        System.out.println("=== Browser Authentication Helper ===");
        System.out.println();
        System.out.println("To get authentication data from your browser:");
        System.out.println();
        
        System.out.println("STEP 1: Open Browser Developer Tools");
        System.out.println("  1. Open your browser (Chrome/Edge/Firefox)");
        System.out.println("  2. Press F12 to open Developer Tools");
        System.out.println("  3. Go to 'Network' tab");
        System.out.println();
        
        System.out.println("STEP 2: Visit Target Website");
        System.out.println("  1. Navigate to: https://www.ps3838.com");
        System.out.println("  2. Login if required");
        System.out.println("  3. Go to the sports data page");
        System.out.println();
        
        System.out.println("STEP 3: Capture API Request");
        System.out.println("  1. Look for requests to: /sports-service/sv/compact/events");
        System.out.println("  2. Right-click on the request");
        System.out.println("  3. Select 'Copy' > 'Copy as cURL'");
        System.out.println();
        
        System.out.println("STEP 4: Extract Headers");
        System.out.println("  From the cURL command, find these headers:");
        System.out.println("  - Cookie: (most important for authentication)");
        System.out.println("  - X-Custid: (customer ID if present)");
        System.out.println("  - X-Browser-Session-Id: (session ID if present)");
        System.out.println("  - Authorization: (auth token if present)");
        System.out.println();
        
        System.out.println("EXAMPLE Cookie format:");
        System.out.println("Cookie: sessionid=abc123; custid=456789; csrftoken=xyz789");
        System.out.println();
        
        System.out.println("STEP 5: Test with Authentication");
        System.out.println("  Run: java -cp target/classes com.zqsport.http.AuthenticatedTest");
        System.out.println("  Enter your cookie string when prompted");
        System.out.println();
        
        System.out.println("ALTERNATIVE METHOD - Copy Full Request:");
        System.out.println("  1. In Network tab, right-click the request");
        System.out.println("  2. Select 'Copy' > 'Copy request headers'");
        System.out.println("  3. Save to a text file for analysis");
        System.out.println();
        
        System.out.println("SECURITY NOTE:");
        System.out.println("  - Never share your cookies publicly");
        System.out.println("  - Cookies contain your login session");
        System.out.println("  - Use them only for testing purposes");
        System.out.println();
        
        System.out.println("TROUBLESHOOTING:");
        System.out.println("  If you don't see the API request:");
        System.out.println("  1. Clear browser cache and reload");
        System.out.println("  2. Try different pages (live scores, odds)");
        System.out.println("  3. Look for XHR/Fetch requests in Network tab");
        System.out.println();
        
        System.out.println("=".repeat(60));
        System.out.println("Ready to test? Run the authenticated test:");
        System.out.println("java -cp target/classes com.zqsport.http.AuthenticatedTest");
        System.out.println("=".repeat(60));
    }
}
