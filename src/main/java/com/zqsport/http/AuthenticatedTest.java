package com.zqsport.http;

import java.util.Scanner;

/**
 * 认证测试程序
 * 使用从浏览器获取的认证信息测试API连接
 */
public class AuthenticatedTest {
    
    public static void main(String[] args) {
        System.out.println("=== Authenticated API Connection Test ===");
        System.out.println();
        
        // 创建高级HTTP客户端
        AdvancedHttpClient client = new AdvancedHttpClient();
        
        try (Scanner scanner = new Scanner(System.in)) {
            
            // 先进行无认证测试
            System.out.println("STEP 1: Testing without authentication...");
            testWithoutAuth(client);
            
            System.out.println("\n" + "=".repeat(60));
            System.out.println("STEP 2: Testing with authentication");
            System.out.println("=".repeat(60));
            
            // 提示用户输入认证信息
            System.out.println("\nPlease follow these steps to get authentication data:");
            System.out.println("1. Open https://www.ps3838.com in your browser");
            System.out.println("2. Open Developer Tools (F12)");
            System.out.println("3. Go to Network tab");
            System.out.println("4. Find a request to /sports-service/sv/compact/events");
            System.out.println("5. Copy the 'Cookie' header value");
            System.out.println();
            
            System.out.print("Enter Cookie header (or press Enter to skip): ");
            String cookieHeader = scanner.nextLine().trim();
            
            if (!cookieHeader.isEmpty()) {
                System.out.println("\nTesting with provided authentication...");
                testWithAuth(client, cookieHeader);
            } else {
                System.out.println("\nSkipping authentication test.");
                System.out.println("To get better results, please provide authentication data.");
            }
            
            // 提供详细的调试信息
            System.out.println("\n" + "=".repeat(60));
            System.out.println("DEBUGGING TIPS:");
            System.out.println("=".repeat(60));
            
            System.out.println("\nIf connection still fails:");
            System.out.println("1. CHECK NETWORK:");
            System.out.println("   - Try accessing the website in browser");
            System.out.println("   - Check if you need VPN/proxy");
            System.out.println("   - Verify firewall settings");
            
            System.out.println("\n2. CHECK AUTHENTICATION:");
            System.out.println("   - Make sure you're logged in to the website");
            System.out.println("   - Copy the complete Cookie header");
            System.out.println("   - Look for additional headers like X-Custid");
            
            System.out.println("\n3. CHECK API ENDPOINT:");
            System.out.println("   - Verify the API URL is correct");
            System.out.println("   - Check if parameters are required");
            System.out.println("   - Look for API documentation");
            
            System.out.println("\n4. TRY DIFFERENT APPROACH:");
            System.out.println("   - Use browser automation (Selenium)");
            System.out.println("   - Try different request methods (POST instead of GET)");
            System.out.println("   - Contact API provider for documentation");
            
        } finally {
            client.close();
        }
        
        System.out.println("\nTest completed!");
    }
    
    /**
     * 测试无认证连接
     */
    private static void testWithoutAuth(AdvancedHttpClient client) {
        String[] testUrls = {
            "https://www.ps3838.com",
            "https://www.ps3838.com/sports-service/sv/compact/events?sp=29&locale=zh_CN"
        };
        
        for (String url : testUrls) {
            System.out.println("\nTesting: " + url);
            String response = client.executeGet(url);
            
            if (response != null && response.length() > 0) {
                System.out.println("SUCCESS: Got response (" + response.length() + " chars)");
                
                // 检查响应内容
                if (response.contains("Access Denied") || response.contains("403") || response.contains("Forbidden")) {
                    System.out.println("WARNING: Response indicates access denied");
                } else if (response.contains("{") || response.contains("[")) {
                    System.out.println("GOOD: Response appears to contain JSON data");
                } else if (response.contains("html")) {
                    System.out.println("INFO: Response is HTML (may be login page or error page)");
                }
                
                // 显示响应预览
                if (response.length() > 150) {
                    System.out.println("Preview: " + response.substring(0, 150) + "...");
                }
            } else {
                System.out.println("FAILED: No response or connection error");
            }
        }
    }
    
    /**
     * 测试带认证的连接
     */
    private static void testWithAuth(AdvancedHttpClient client, String cookieHeader) {
        System.out.println("Using Cookie: " + maskSensitiveInfo(cookieHeader));
        
        String[] testUrls = {
            "https://www.ps3838.com/sports-service/sv/compact/events?sp=29&locale=zh_CN",
            "https://www.ps3838.com/sports-service/sv/compact/events?sp=1&locale=zh_CN"  // 尝试不同的sport ID
        };
        
        for (String url : testUrls) {
            System.out.println("\nTesting with auth: " + url);
            String response = client.executeGet(url, cookieHeader);
            
            if (response != null && response.length() > 0) {
                System.out.println("SUCCESS: Got authenticated response (" + response.length() + " chars)");
                
                // 详细分析响应
                analyzeResponse(response);
                
            } else {
                System.out.println("FAILED: No response even with authentication");
                System.out.println("SUGGESTIONS:");
                System.out.println("  - Check if cookie is complete and current");
                System.out.println("  - Try copying cookie from a fresh browser session");
                System.out.println("  - Look for additional required headers");
            }
        }
    }
    
    /**
     * 分析API响应
     */
    private static void analyzeResponse(String response) {
        if (response.startsWith("{") || response.startsWith("[")) {
            System.out.println("✅ EXCELLENT: Valid JSON response received!");
            
            // 尝试识别数据类型
            if (response.contains("leagues") || response.contains("events") || response.contains("odds")) {
                System.out.println("✅ Sports data detected in response");
            }
            
            // 显示JSON结构预览
            if (response.length() > 200) {
                System.out.println("JSON Preview: " + response.substring(0, 200) + "...");
            } else {
                System.out.println("Full JSON: " + response);
            }
            
        } else if (response.toLowerCase().contains("login") || response.toLowerCase().contains("signin")) {
            System.out.println("⚠️ WARNING: Response suggests authentication required");
            System.out.println("  - Cookie may be expired");
            System.out.println("  - Need to login again in browser");
            
        } else if (response.toLowerCase().contains("error") || response.toLowerCase().contains("denied")) {
            System.out.println("❌ ERROR: Access denied or error response");
            System.out.println("  - API may require special permissions");
            System.out.println("  - Check if endpoint URL is correct");
            
        } else {
            System.out.println("📄 INFO: Got response but format unclear");
            if (response.length() > 150) {
                System.out.println("Preview: " + response.substring(0, 150) + "...");
            }
        }
    }
    
    /**
     * 隐藏敏感信息
     */
    private static String maskSensitiveInfo(String cookie) {
        if (cookie.length() > 50) {
            return cookie.substring(0, 20) + "***[MASKED]***" + cookie.substring(cookie.length() - 10);
        }
        return cookie.substring(0, Math.min(cookie.length(), 20)) + "***";
    }
}
