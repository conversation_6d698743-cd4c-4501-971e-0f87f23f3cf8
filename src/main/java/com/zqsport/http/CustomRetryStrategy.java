package com.zqsport.http;

import org.apache.hc.client5.http.HttpRequestRetryStrategy;
import org.apache.hc.core5.http.HttpRequest;
import org.apache.hc.core5.http.HttpResponse;
import org.apache.hc.core5.http.protocol.HttpContext;
import org.apache.hc.core5.util.TimeValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLException;
import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 自定义HTTP重试策略
 * 针对网络连接问题进行智能重试
 */
public class CustomRetryStrategy implements HttpRequestRetryStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomRetryStrategy.class);
    
    private final int maxRetries;
    private final Set<Class<? extends IOException>> retryableExceptions;
    private final Set<Integer> retryableStatusCodes;
    
    public CustomRetryStrategy() {
        this.maxRetries = HttpRetryConfig.DEFAULT_MAX_RETRIES;
        
        // 可重试的异常类型
        this.retryableExceptions = new HashSet<>(Arrays.asList(
            SocketTimeoutException.class,
            ConnectException.class,
            SocketException.class
            // 注意：不包括 UnknownHostException 和 SSLException，这些通常不应重试
        ));
        
        // 可重试的HTTP状态码
        this.retryableStatusCodes = new HashSet<>(Arrays.asList(
            408, // Request Timeout
            429, // Too Many Requests
            500, // Internal Server Error
            502, // Bad Gateway
            503, // Service Unavailable
            504  // Gateway Timeout
        ));
    }
    
    @Override
    public boolean retryRequest(HttpRequest request, IOException exception, int execCount, HttpContext context) {
        logger.debug("检查是否重试请求 - 执行次数: {}, 异常: {}", execCount, exception.getClass().getSimpleName());
        
        // 超过最大重试次数
        if (execCount > maxRetries) {
            logger.warn("❌ 已达到最大重试次数 ({}), 不再重试", maxRetries);
            return false;
        }
        
        // 检查异常类型是否可重试
        boolean isRetryableException = isRetryableException(exception);
        
        if (isRetryableException) {
            logger.info("🔄 第{}次重试 - 异常类型: {}", execCount, exception.getClass().getSimpleName());
            return true;
        } else {
            logger.warn("❌ 不可重试的异常: {}", exception.getClass().getSimpleName());
            return false;
        }
    }
    
    @Override
    public boolean retryRequest(HttpResponse response, int execCount, HttpContext context) {
        int statusCode = response.getCode();
        logger.debug("检查HTTP响应是否需要重试 - 状态码: {}, 执行次数: {}", statusCode, execCount);
        
        // 超过最大重试次数
        if (execCount > maxRetries) {
            logger.warn("❌ 已达到最大重试次数 ({}), 不再重试", maxRetries);
            return false;
        }
        
        // 检查状态码是否可重试
        if (retryableStatusCodes.contains(statusCode)) {
            logger.info("🔄 第{}次重试 - HTTP状态码: {}", execCount, statusCode);
            return true;
        }
        
        return false;
    }
    
    @Override
    public TimeValue getRetryInterval(HttpResponse response, int execCount, HttpContext context) {
        return calculateRetryDelay(execCount, response.getCode());
    }
    
    @Override
    public TimeValue getRetryInterval(HttpRequest request, IOException exception, int execCount, HttpContext context) {
        return calculateRetryDelay(execCount, 0);
    }
    
    /**
     * 检查异常是否可重试
     */
    private boolean isRetryableException(IOException exception) {
        // 直接检查异常类型
        if (retryableExceptions.contains(exception.getClass())) {
            return true;
        }
        
        // 检查异常消息中的关键词
        String message = exception.getMessage();
        if (message != null) {
            message = message.toLowerCase();
            
            // 连接重置通常可以重试
            if (message.contains("connection reset")) {
                logger.debug("检测到连接重置，允许重试");
                return true;
            }
            
            // 连接超时可以重试
            if (message.contains("timeout")) {
                logger.debug("检测到超时，允许重试");
                return true;
            }
            
            // 连接被拒绝可以重试（可能是临时问题）
            if (message.contains("connection refused")) {
                logger.debug("检测到连接被拒绝，允许重试");
                return true;
            }
        }
        
        // 特殊处理：SSL异常通常不应重试
        if (exception instanceof SSLException) {
            logger.debug("SSL异常通常不重试");
            return false;
        }
        
        // 特殊处理：DNS解析失败通常不应重试
        if (exception instanceof UnknownHostException) {
            logger.debug("DNS解析失败通常不重试");
            return false;
        }
        
        return false;
    }
    
    /**
     * 计算重试延迟时间
     */
    private TimeValue calculateRetryDelay(int execCount, int statusCode) {
        // 基础延迟时间
        long baseDelay = HttpRetryConfig.DEFAULT_BASE_DELAY_MS;
        
        // 根据状态码调整延迟倍数
        int multiplier = getDelayMultiplier(statusCode);
        
        // 指数退避算法
        long delay = HttpRetryConfig.calculateRetryDelay(
            execCount, 
            baseDelay * multiplier, 
            HttpRetryConfig.DEFAULT_BACKOFF_MULTIPLIER, 
            HttpRetryConfig.DEFAULT_MAX_DELAY_MS
        );
        
        logger.debug("计算重试延迟: 执行次数={}, 状态码={}, 延迟={}ms", execCount, statusCode, delay);
        
        return TimeValue.ofMilliseconds(delay);
    }
    
    /**
     * 根据状态码获取延迟倍数
     */
    private int getDelayMultiplier(int statusCode) {
        switch (statusCode) {
            case 429: // Too Many Requests - 需要更长延迟
                return 5;
            case 503: // Service Unavailable - 需要较长延迟
                return 3;
            case 502: // Bad Gateway
            case 504: // Gateway Timeout
            default:
                return 1;
        }
    }
    
    /**
     * 获取最大重试次数
     */
    public int getMaxRetries() {
        return maxRetries;
    }
}
