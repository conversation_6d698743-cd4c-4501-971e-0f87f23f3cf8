package com.zqsport.http.parser;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zqsport.http.model.FootballData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 足球数据解析器
 */
public class FootballDataParser {
    
    private static final Logger logger = LoggerFactory.getLogger(FootballDataParser.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 解析联赛数据
     */
    public FootballData.League parseLeagueData(String jsonData) {
        try {
            @SuppressWarnings("unchecked")
            List<Object> leagueArray = objectMapper.readValue(jsonData, List.class);
            
            if (leagueArray.size() < 3) {
                logger.warn("联赛数据格式不正确，缺少必要字段");
                return null;
            }
            
            // 解析联赛信息
            int leagueId = (Integer) leagueArray.get(0);
            String leagueName = (String) leagueArray.get(1);
            
            @SuppressWarnings("unchecked")
            List<List<Object>> matchesData = (List<List<Object>>) leagueArray.get(2);
            
            List<FootballData.Match> matches = new ArrayList<>();
            
            for (List<Object> matchData : matchesData) {
                FootballData.Match match = parseMatchData(matchData);
                if (match != null) {
                    matches.add(match);
                }
            }
            
            return new FootballData.League(leagueId, leagueName, matches);
            
        } catch (Exception e) {
            logger.error("解析联赛数据失败", e);
            return null;
        }
    }
    
    /**
     * 解析比赛数据
     */
    private FootballData.Match parseMatchData(List<Object> matchData) {
        try {
            if (matchData.size() < 10) {
                logger.warn("比赛数据字段不足");
                return null;
            }
            
            // 基本信息
            long matchId = ((Number) matchData.get(0)).longValue();
            String homeTeam = (String) matchData.get(1);
            String awayTeam = (String) matchData.get(2);
            int matchStatus = (Integer) matchData.get(3);
            long matchTime = ((Number) matchData.get(4)).longValue();
            
            FootballData.Match match = new FootballData.Match(matchId, homeTeam, awayTeam, matchStatus, matchTime);
            
            // 赔率数据 (索引8)
            if (matchData.size() > 8 && matchData.get(8) instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> oddsMap = (Map<String, Object>) matchData.get(8);
                FootballData.Match.OddsInfo oddsInfo = parseOddsData(oddsMap);
                match.setOdds(oddsInfo);
            }
            
            // 比分信息 (索引9-11)
            if (matchData.size() > 11) {
                @SuppressWarnings("unchecked")
                List<Integer> fullScore = (List<Integer>) matchData.get(9);
                @SuppressWarnings("unchecked")
                List<Integer> halfScore = (List<Integer>) matchData.get(10);
                
                if (fullScore != null && fullScore.size() >= 2 && 
                    halfScore != null && halfScore.size() >= 2) {
                    FootballData.Match.Score score = new FootballData.Match.Score(
                        fullScore.get(0), fullScore.get(1),
                        halfScore.get(0), halfScore.get(1)
                    );
                    match.setScore(score);
                }
            }
            
            // 比赛时间和状态信息
            if (matchData.size() > 17) {
                String currentTime = (String) matchData.get(17);
                String period = (String) matchData.get(18);
                match.setCurrentTime(currentTime);
                match.setPeriod(period);
            }
            
            // 英文队名
            if (matchData.size() > 27) {
                String homeTeamEn = (String) matchData.get(25);
                String awayTeamEn = (String) matchData.get(26);
                match.setHomeTeamEn(homeTeamEn);
                match.setAwayTeamEn(awayTeamEn);
            }
            
            return match;
            
        } catch (Exception e) {
            logger.error("解析比赛数据失败", e);
            return null;
        }
    }
    
    /**
     * 解析赔率数据
     */
    private FootballData.Match.OddsInfo parseOddsData(Map<String, Object> oddsMap) {
        FootballData.Match.OddsInfo oddsInfo = new FootballData.Match.OddsInfo();
        
        try {
            // 解析主盘口 ("0")
            if (oddsMap.containsKey("0")) {
                @SuppressWarnings("unchecked")
                List<Object> mainMarket = (List<Object>) oddsMap.get("0");
                parseMainMarket(mainMarket, oddsInfo);
            }
            
            // 解析滚球盘口 ("1") - 如果有的话
            if (oddsMap.containsKey("1")) {
                @SuppressWarnings("unchecked")
                List<Object> liveMarket = (List<Object>) oddsMap.get("1");
                parseLiveMarket(liveMarket, oddsInfo);
            }
            
        } catch (Exception e) {
            logger.error("解析赔率数据失败", e);
        }
        
        return oddsInfo;
    }
    
    /**
     * 解析主盘口数据
     */
    private void parseMainMarket(List<Object> marketData, FootballData.Match.OddsInfo oddsInfo) {
        try {
            if (marketData.size() < 3) return;
            
            // 让球盘数据 (索引0)
            @SuppressWarnings("unchecked")
            List<List<Object>> handicapData = (List<List<Object>>) marketData.get(0);
            List<FootballData.Match.OddsInfo.HandicapOdds> handicapOdds = parseHandicapOdds(handicapData);
            oddsInfo.setHandicapOdds(handicapOdds);
            
            // 大小球盘数据 (索引1)
            @SuppressWarnings("unchecked")
            List<List<Object>> totalData = (List<List<Object>>) marketData.get(1);
            List<FootballData.Match.OddsInfo.TotalOdds> totalOdds = parseTotalOdds(totalData);
            oddsInfo.setTotalOdds(totalOdds);
            
            // 胜平负数据 (索引2)
            @SuppressWarnings("unchecked")
            List<Object> winDrawWinData = (List<Object>) marketData.get(2);
            FootballData.Match.OddsInfo.WinDrawWinOdds winDrawWinOdds = parseWinDrawWinOdds(winDrawWinData);
            oddsInfo.setWinDrawWinOdds(winDrawWinOdds);
            
        } catch (Exception e) {
            logger.error("解析主盘口数据失败", e);
        }
    }
    
    /**
     * 解析滚球盘口数据
     */
    private void parseLiveMarket(List<Object> marketData, FootballData.Match.OddsInfo oddsInfo) {
        // 滚球盘口的解析逻辑类似主盘口，这里可以根据需要实现
        logger.info("发现滚球盘口数据");
    }
    
    /**
     * 解析让球盘赔率
     */
    private List<FootballData.Match.OddsInfo.HandicapOdds> parseHandicapOdds(List<List<Object>> handicapData) {
        List<FootballData.Match.OddsInfo.HandicapOdds> result = new ArrayList<>();
        
        for (List<Object> odds : handicapData) {
            try {
                if (odds.size() >= 11) {
                    double handicap = ((Number) odds.get(0)).doubleValue();
                    String handicapDisplay = (String) odds.get(2);
                    double homeOdds = Double.parseDouble((String) odds.get(3));
                    double awayOdds = Double.parseDouble((String) odds.get(4));
                    double maxBet = ((Number) odds.get(9)).doubleValue();
                    
                    FootballData.Match.OddsInfo.HandicapOdds handicapOdds = 
                        new FootballData.Match.OddsInfo.HandicapOdds(handicap, handicapDisplay, homeOdds, awayOdds, maxBet);
                    result.add(handicapOdds);
                }
            } catch (Exception e) {
                logger.debug("解析让球盘赔率项失败: {}", e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 解析大小球盘赔率
     */
    private List<FootballData.Match.OddsInfo.TotalOdds> parseTotalOdds(List<List<Object>> totalData) {
        List<FootballData.Match.OddsInfo.TotalOdds> result = new ArrayList<>();
        
        for (List<Object> odds : totalData) {
            try {
                if (odds.size() >= 8) {
                    String totalDisplay = (String) odds.get(0);
                    double total = ((Number) odds.get(1)).doubleValue();
                    double overOdds = Double.parseDouble((String) odds.get(2));
                    double underOdds = Double.parseDouble((String) odds.get(3));
                    double maxBet = ((Number) odds.get(6)).doubleValue();
                    
                    FootballData.Match.OddsInfo.TotalOdds totalOdds = 
                        new FootballData.Match.OddsInfo.TotalOdds(total, totalDisplay, overOdds, underOdds, maxBet);
                    result.add(totalOdds);
                }
            } catch (Exception e) {
                logger.debug("解析大小球盘赔率项失败: {}", e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 解析胜平负赔率
     */
    private FootballData.Match.OddsInfo.WinDrawWinOdds parseWinDrawWinOdds(List<Object> winDrawWinData) {
        try {
            if (winDrawWinData.size() >= 6 && winDrawWinData.get(0) instanceof String) {
                double homeWin = Double.parseDouble((String) winDrawWinData.get(0));
                double draw = Double.parseDouble((String) winDrawWinData.get(1));
                double awayWin = Double.parseDouble((String) winDrawWinData.get(2));
                double maxBet = ((Number) winDrawWinData.get(5)).doubleValue();
                
                return new FootballData.Match.OddsInfo.WinDrawWinOdds(homeWin, draw, awayWin, maxBet);
            }
        } catch (Exception e) {
            logger.debug("解析胜平负赔率失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 打印联赛数据
     */
    public void printLeagueData(FootballData.League league) {
        if (league == null) {
            logger.warn("联赛数据为空");
            return;
        }
        
        System.out.println("=".repeat(80));
        System.out.printf("🏆 联赛: %s (ID: %d)\n", league.getLeagueName(), league.getLeagueId());
        System.out.printf("📊 比赛总数: %d 场\n", league.getMatches().size());
        System.out.println("=".repeat(80));
        
        for (int i = 0; i < league.getMatches().size(); i++) {
            FootballData.Match match = league.getMatches().get(i);
            System.out.printf("\n📍 比赛 %d:\n", i + 1);
            System.out.print(match.toString());
            
            // 打印赔率信息
            if (match.getOdds() != null) {
                printOddsInfo(match.getOdds());
            }
            
            System.out.println("-".repeat(60));
        }
    }
    
    /**
     * 打印赔率信息
     */
    private void printOddsInfo(FootballData.Match.OddsInfo odds) {
        // 让球盘
        if (odds.getHandicapOdds() != null && !odds.getHandicapOdds().isEmpty()) {
            System.out.println("   🎯 让球盘:");
            for (FootballData.Match.OddsInfo.HandicapOdds handicap : odds.getHandicapOdds()) {
                System.out.println("      " + handicap.toString());
            }
        }
        
        // 大小球盘
        if (odds.getTotalOdds() != null && !odds.getTotalOdds().isEmpty()) {
            System.out.println("   🎲 大小球盘:");
            for (FootballData.Match.OddsInfo.TotalOdds total : odds.getTotalOdds()) {
                System.out.println("      " + total.toString());
            }
        }
        
        // 胜平负
        if (odds.getWinDrawWinOdds() != null) {
            System.out.println("   ⚽ " + odds.getWinDrawWinOdds().toString());
        }
    }
    
    /**
     * 格式化时间戳
     */
    private String formatTimestamp(long timestamp) {
        try {
            Date date = new Date(timestamp);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        } catch (Exception e) {
            return "时间格式错误";
        }
    }
}
