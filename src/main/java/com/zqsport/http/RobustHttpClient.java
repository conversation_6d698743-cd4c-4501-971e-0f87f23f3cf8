package com.zqsport.http;

import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.hc.core5.util.Timeout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.util.concurrent.TimeUnit;

/**
 * 强化的HTTP客户端，专门处理网络连接问题
 * 包含代理支持、SSL绕过、重试机制等
 */
public class RobustHttpClient {
    
    private static final Logger logger = LoggerFactory.getLogger(RobustHttpClient.class);
    
    private CloseableHttpClient httpClient;
    private boolean isInitialized = false;
    
    /**
     * 初始化强化的HTTP客户端
     */
    public void initialize() {
        try {
            logger.info("🔧 初始化强化HTTP客户端...");
            
            // 创建信任所有证书的SSL上下文
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(TrustAllStrategy.INSTANCE)
                    .build();
            
            // SSL连接工厂（绕过主机名验证）
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext, 
                    NoopHostnameVerifier.INSTANCE
            );
            
            // 注册连接工厂
            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder
                    .<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", sslSocketFactory)
                    .build();
            
            // 连接池管理器
            PoolingHttpClientConnectionManager connectionManager = 
                    new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            connectionManager.setMaxTotal(HttpConfig.MAX_TOTAL_CONNECTIONS);
            connectionManager.setDefaultMaxPerRoute(HttpConfig.MAX_CONNECTIONS_PER_ROUTE);
            
            // 请求配置
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(Timeout.ofMilliseconds(HttpConfig.CONNECTION_TIMEOUT))
                    .setResponseTimeout(Timeout.ofMilliseconds(HttpConfig.REQUEST_TIMEOUT))
                    .setConnectionRequestTimeout(Timeout.ofMilliseconds(5000))
                    .build();
            
            // 创建HTTP客户端
            this.httpClient = HttpClients.custom()
                    .setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(requestConfig)
                    .setRetryStrategy(new CustomRetryStrategy())
                    .build();
            
            this.isInitialized = true;
            logger.info("✅ 强化HTTP客户端初始化成功");
            
        } catch (Exception e) {
            logger.error("❌ 强化HTTP客户端初始化失败", e);
            // 降级到默认客户端
            this.httpClient = HttpClients.createDefault();
            this.isInitialized = true;
        }
    }
    
    /**
     * 执行GET请求（带完整错误处理）
     */
    public String executeGet(String url) {
        if (!isInitialized) {
            initialize();
        }
        
        logger.info("🌐 发送请求: {}", url);
        
        HttpGet request = new HttpGet(url);
        setupRequestHeaders(request);
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            
            logger.info("📊 响应状态: {}, 长度: {} 字符", statusCode, responseBody.length());
            
            if (statusCode >= 200 && statusCode < 300) {
                logger.info("✅ 请求成功");
                return responseBody;
            } else if (statusCode >= 400 && statusCode < 500) {
                logger.warn("⚠️ 客户端错误 ({}): 可能需要认证或权限", statusCode);
                return handleClientError(statusCode, responseBody);
            } else if (statusCode >= 500) {
                logger.warn("⚠️ 服务器错误 ({}): 服务器暂时不可用", statusCode);
                return handleServerError(statusCode, responseBody);
            }
            
            return responseBody;
            
        } catch (java.net.ConnectException e) {
            logger.error("❌ 连接被拒绝: {}", e.getMessage());
            return handleConnectionRefused();
        } catch (java.net.SocketTimeoutException e) {
            logger.error("❌ 连接超时: {}", e.getMessage());
            return handleTimeout();
        } catch (javax.net.ssl.SSLException e) {
            logger.error("❌ SSL错误: {}", e.getMessage());
            return handleSSLError();
        } catch (Exception e) {
            logger.error("❌ 请求失败: {}", e.getMessage(), e);
            return handleGeneralError(e);
        }
    }
    
    /**
     * 设置请求头
     */
    private void setupRequestHeaders(HttpGet request) {
        request.setHeader("User-Agent", HttpConfig.USER_AGENT);
        request.setHeader("Accept", "application/json, text/plain, */*");
        request.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        request.setHeader("Accept-Encoding", "gzip, deflate, br");
        request.setHeader("Connection", "keep-alive");
        request.setHeader("Cache-Control", "no-cache");
        request.setHeader("Pragma", "no-cache");
        
        // 如果是目标API，添加特殊头部
        try {
            if (request.getUri().toString().contains("ps3838.com")) {
                request.setHeader("Origin", HttpConfig.ORIGIN);
                request.setHeader("Referer", HttpConfig.REFERER);
                request.setHeader("Sec-Fetch-Dest", "empty");
                request.setHeader("Sec-Fetch-Mode", "cors");
                request.setHeader("Sec-Fetch-Site", "same-origin");
            }
        } catch (Exception e) {
            logger.debug("设置特殊头部时出错: {}", e.getMessage());
        }
    }
    
    /**
     * 处理连接被拒绝错误
     */
    private String handleConnectionRefused() {
        logger.error("💡 解决建议:");
        logger.error("  1. 检查目标网站是否可访问");
        logger.error("  2. 尝试使用VPN或代理");
        logger.error("  3. 检查防火墙设置");
        logger.error("  4. 确认API端点是否正确");
        return createErrorResponse("CONNECTION_REFUSED", "连接被目标服务器拒绝");
    }
    
    /**
     * 处理超时错误
     */
    private String handleTimeout() {
        logger.error("💡 解决建议:");
        logger.error("  1. 检查网络连接速度");
        logger.error("  2. 增加超时时间设置");
        logger.error("  3. 尝试在网络较好的环境下运行");
        return createErrorResponse("TIMEOUT", "请求超时");
    }
    
    /**
     * 处理SSL错误
     */
    private String handleSSLError() {
        logger.error("💡 解决建议:");
        logger.error("  1. 证书验证问题，已自动绕过");
        logger.error("  2. 如果仍有问题，请检查系统时间");
        logger.error("  3. 更新Java版本");
        return createErrorResponse("SSL_ERROR", "SSL证书验证失败");
    }
    
    /**
     * 处理客户端错误
     */
    private String handleClientError(int statusCode, String responseBody) {
        if (statusCode == 401) {
            logger.error("💡 需要认证 - 可能需要登录或API密钥");
        } else if (statusCode == 403) {
            logger.error("💡 访问被禁止 - 可能有IP限制或需要特殊权限");
        } else if (statusCode == 404) {
            logger.error("💡 API端点不存在 - 请检查URL是否正确");
        }
        
        return createErrorResponse("CLIENT_ERROR_" + statusCode, "客户端错误: " + statusCode);
    }
    
    /**
     * 处理服务器错误
     */
    private String handleServerError(int statusCode, String responseBody) {
        logger.error("💡 服务器错误 - 稍后重试或联系服务提供商");
        return createErrorResponse("SERVER_ERROR_" + statusCode, "服务器错误: " + statusCode);
    }
    
    /**
     * 处理一般错误
     */
    private String handleGeneralError(Exception e) {
        return createErrorResponse("GENERAL_ERROR", "请求失败: " + e.getMessage());
    }
    
    /**
     * 创建错误响应
     */
    private String createErrorResponse(String errorCode, String errorMessage) {
        return String.format("{\"error\": \"%s\", \"message\": \"%s\", \"timestamp\": %d}", 
                errorCode, errorMessage, System.currentTimeMillis());
    }
    
    /**
     * 关闭客户端
     */
    public void close() {
        if (httpClient != null) {
            try {
                httpClient.close();
                logger.info("🔒 HTTP客户端已关闭");
            } catch (Exception e) {
                logger.warn("关闭HTTP客户端时出错", e);
            }
        }
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        RobustHttpClient client = new RobustHttpClient();
        
        // 测试基本连接
        logger.info("🧪 测试基本网络连接...");
        String result1 = client.executeGet("https://httpbin.org/get");
        logger.info("结果1: {}", result1 != null ? "成功" : "失败");
        
        // 测试目标API
        logger.info("🧪 测试目标API连接...");
        String apiUrl = HttpConfig.buildOddsApiUrl() + "?" + 
                HttpConfig.buildQueryParams(29, "zh_CN");
        String result2 = client.executeGet(apiUrl);
        logger.info("结果2: {}", result2 != null ? "成功" : "失败");
        
        client.close();
    }
}
