package com.zqsport.http;

import com.zqsport.http.model.FootballData;
import com.zqsport.http.parser.FootballDataParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 足球数据解析测试
 */
public class FootballDataTest {
    
    private static final Logger logger = LoggerFactory.getLogger(FootballDataTest.class);
    
    public static void main(String[] args) {
        FootballDataParser parser = new FootballDataParser();
        
        // 测试数据 - 您提供的足球JSON数据
        String testData = "[\n" +
                "    198316,\n" +
                "    \"英格兰 - 职业发展联赛 U21\",\n" +
                "    [\n" +
                "        [\n" +
                "            1613061595,\n" +
                "            \"查尔顿竞技\",\n" +
                "            \"谢菲尔德星期三\",\n" +
                "            4,\n" +
                "            1755000000000,\n" +
                "            1,\n" +
                "            0,\n" +
                "            2,\n" +
                "            {\n" +
                "                \"0\": [\n" +
                "                    [\n" +
                "                        [\n" +
                "                            0.75,\n" +
                "                            -0.75,\n" +
                "                            \"0.5-1\",\n" +
                "                            \"1.870\",\n" +
                "                            \"0.401\",\n" +
                "                            1,\n" +
                "                            0,\n" +
                "                            50078440143,\n" +
                "                            1,\n" +
                "                            550.00,\n" +
                "                            1\n" +
                "                        ],\n" +
                "                        [\n" +
                "                            0.5,\n" +
                "                            -0.5,\n" +
                "                            \"0.5\",\n" +
                "                            \"1.250\",\n" +
                "                            \"0.641\",\n" +
                "                            1,\n" +
                "                            0,\n" +
                "                            50078440144,\n" +
                "                            1,\n" +
                "                            550.00,\n" +
                "                            1\n" +
                "                        ],\n" +
                "                        [\n" +
                "                            0.25,\n" +
                "                            -0.25,\n" +
                "                            \"0-0.5\",\n" +
                "                            \"0.746\",\n" +
                "                            \"1.080\",\n" +
                "                            1,\n" +
                "                            0,\n" +
                "                            3212326804,\n" +
                "                            0,\n" +
                "                            550.00,\n" +
                "                            1\n" +
                "                        ]\n" +
                "                    ],\n" +
                "                    [\n" +
                "                        [\n" +
                "                            \"5-5.5\",\n" +
                "                            5.25,\n" +
                "                            \"2.250\",\n" +
                "                            \"0.320\",\n" +
                "                            50078440156,\n" +
                "                            1,\n" +
                "                            550.00,\n" +
                "                            1\n" +
                "                        ],\n" +
                "                        [\n" +
                "                            \"5.0\",\n" +
                "                            5.0,\n" +
                "                            \"1.590\",\n" +
                "                            \"0.490\",\n" +
                "                            50078440155,\n" +
                "                            1,\n" +
                "                            550.00,\n" +
                "                            1\n" +
                "                        ]\n" +
                "                    ],\n" +
                "                    [\n" +
                "                        \"\",\n" +
                "                        \"\",\n" +
                "                        null,\n" +
                "                        0,\n" +
                "                        0,\n" +
                "                        null,\n" +
                "                        0\n" +
                "                    ],\n" +
                "                    0,\n" +
                "                    null,\n" +
                "                    1,\n" +
                "                    0,\n" +
                "                    [\n" +
                "                        2,\n" +
                "                        2\n" +
                "                    ],\n" +
                "                    2,\n" +
                "                    [\n" +
                "                        4,\n" +
                "                        0\n" +
                "                    ],\n" +
                "                    [\n" +
                "                        0,\n" +
                "                        0\n" +
                "                    ],\n" +
                "                    1\n" +
                "                ]\n" +
                "            },\n" +
                "            [\n" +
                "                4,\n" +
                "                0\n" +
                "            ],\n" +
                "            [\n" +
                "                0,\n" +
                "                0\n" +
                "            ],\n" +
                "            [\n" +
                "                0,\n" +
                "                1\n" +
                "            ],\n" +
                "            0,\n" +
                "            null,\n" +
                "            null,\n" +
                "            \"29'\",\n" +
                "            \"2H\",\n" +
                "            \"O\",\n" +
                "            0,\n" +
                "            0,\n" +
                "            0,\n" +
                "            10,\n" +
                "            2,\n" +
                "            0,\n" +
                "            \"Charlton Athletic\",\n" +
                "            \"Sheffield Wednesday\",\n" +
                "            0,\n" +
                "            \"Regular\",\n" +
                "            1612596162,\n" +
                "            0,\n" +
                "            0,\n" +
                "            0,\n" +
                "            \"Goals\",\n" +
                "            null,\n" +
                "            0,\n" +
                "            null\n" +
                "        ]\n" +
                "    ],\n" +
                "    null,\n" +
                "    \"England - Professional Development League U21\",\n" +
                "    0\n" +
                "]";

        logger.info("🧪 开始足球数据解析测试...");
        
        try {
            FootballData.League league = parser.parseLeagueData(testData);
            
            if (league != null) {
                logger.info("✅ 解析成功！");
                parser.printLeagueData(league);
            } else {
                logger.error("❌ 解析失败！");
            }
            
        } catch (Exception e) {
            logger.error("测试过程中发生错误", e);
        }
    }
}
