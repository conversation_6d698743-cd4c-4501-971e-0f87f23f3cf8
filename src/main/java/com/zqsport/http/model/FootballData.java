package com.zqsport.http.model;

import java.util.List;
import java.util.Map;

/**
 * 足球数据模型
 */
public class FootballData {
    
    /**
     * 联赛信息
     */
    public static class League {
        private int leagueId;
        private String leagueName;
        private List<Match> matches;
        
        public League(int leagueId, String leagueName, List<Match> matches) {
            this.leagueId = leagueId;
            this.leagueName = leagueName;
            this.matches = matches;
        }
        
        // Getters
        public int getLeagueId() { return leagueId; }
        public String getLeagueName() { return leagueName; }
        public List<Match> getMatches() { return matches; }
    }
    
    /**
     * 比赛信息
     */
    public static class Match {
        private long matchId;
        private String homeTeam;
        private String awayTeam;
        private int matchStatus;
        private long matchTime;
        private String currentTime;
        private String period;
        private Score score;
        private OddsInfo odds;
        private String homeTeamEn;
        private String awayTeamEn;
        
        // 比分信息
        public static class Score {
            private int homeScore;
            private int awayScore;
            private int homeHalfScore;
            private int awayHalfScore;
            
            public Score(int homeScore, int awayScore, int homeHalfScore, int awayHalfScore) {
                this.homeScore = homeScore;
                this.awayScore = awayScore;
                this.homeHalfScore = homeHalfScore;
                this.awayHalfScore = awayHalfScore;
            }
            
            public int getHomeScore() { return homeScore; }
            public int getAwayScore() { return awayScore; }
            public int getHomeHalfScore() { return homeHalfScore; }
            public int getAwayHalfScore() { return awayHalfScore; }
            
            @Override
            public String toString() {
                return String.format("%d-%d (半场: %d-%d)", homeScore, awayScore, homeHalfScore, awayHalfScore);
            }
        }
        
        // 赔率信息
        public static class OddsInfo {
            private List<HandicapOdds> handicapOdds; // 让球盘
            private List<TotalOdds> totalOdds; // 大小球盘
            private WinDrawWinOdds winDrawWinOdds; // 胜平负
            
            // 让球盘赔率
            public static class HandicapOdds {
                private double handicap;
                private String handicapDisplay;
                private double homeOdds;
                private double awayOdds;
                private double maxBet;
                
                public HandicapOdds(double handicap, String handicapDisplay, double homeOdds, double awayOdds, double maxBet) {
                    this.handicap = handicap;
                    this.handicapDisplay = handicapDisplay;
                    this.homeOdds = homeOdds;
                    this.awayOdds = awayOdds;
                    this.maxBet = maxBet;
                }
                
                public double getHandicap() { return handicap; }
                public String getHandicapDisplay() { return handicapDisplay; }
                public double getHomeOdds() { return homeOdds; }
                public double getAwayOdds() { return awayOdds; }
                public double getMaxBet() { return maxBet; }
                
                @Override
                public String toString() {
                    return String.format("让球%s: 主队%.3f 客队%.3f (最大投注:%.2f)", 
                            handicapDisplay, homeOdds, awayOdds, maxBet);
                }
            }
            
            // 大小球盘赔率
            public static class TotalOdds {
                private double total;
                private String totalDisplay;
                private double overOdds;
                private double underOdds;
                private double maxBet;
                
                public TotalOdds(double total, String totalDisplay, double overOdds, double underOdds, double maxBet) {
                    this.total = total;
                    this.totalDisplay = totalDisplay;
                    this.overOdds = overOdds;
                    this.underOdds = underOdds;
                    this.maxBet = maxBet;
                }
                
                public double getTotal() { return total; }
                public String getTotalDisplay() { return totalDisplay; }
                public double getOverOdds() { return overOdds; }
                public double getUnderOdds() { return underOdds; }
                public double getMaxBet() { return maxBet; }
                
                @Override
                public String toString() {
                    return String.format("大小球%s: 大%.3f 小%.3f (最大投注:%.2f)", 
                            totalDisplay, overOdds, underOdds, maxBet);
                }
            }
            
            // 胜平负赔率
            public static class WinDrawWinOdds {
                private double homeWin;
                private double draw;
                private double awayWin;
                private double maxBet;
                
                public WinDrawWinOdds(double homeWin, double draw, double awayWin, double maxBet) {
                    this.homeWin = homeWin;
                    this.draw = draw;
                    this.awayWin = awayWin;
                    this.maxBet = maxBet;
                }
                
                public double getHomeWin() { return homeWin; }
                public double getDraw() { return draw; }
                public double getAwayWin() { return awayWin; }
                public double getMaxBet() { return maxBet; }
                
                @Override
                public String toString() {
                    return String.format("胜平负: 主胜%.3f 平局%.3f 客胜%.3f (最大投注:%.2f)", 
                            homeWin, draw, awayWin, maxBet);
                }
            }
            
            public List<HandicapOdds> getHandicapOdds() { return handicapOdds; }
            public List<TotalOdds> getTotalOdds() { return totalOdds; }
            public WinDrawWinOdds getWinDrawWinOdds() { return winDrawWinOdds; }
            
            public void setHandicapOdds(List<HandicapOdds> handicapOdds) { this.handicapOdds = handicapOdds; }
            public void setTotalOdds(List<TotalOdds> totalOdds) { this.totalOdds = totalOdds; }
            public void setWinDrawWinOdds(WinDrawWinOdds winDrawWinOdds) { this.winDrawWinOdds = winDrawWinOdds; }
        }
        
        // 构造函数和Getters
        public Match(long matchId, String homeTeam, String awayTeam, int matchStatus, long matchTime) {
            this.matchId = matchId;
            this.homeTeam = homeTeam;
            this.awayTeam = awayTeam;
            this.matchStatus = matchStatus;
            this.matchTime = matchTime;
        }
        
        public long getMatchId() { return matchId; }
        public String getHomeTeam() { return homeTeam; }
        public String getAwayTeam() { return awayTeam; }
        public int getMatchStatus() { return matchStatus; }
        public long getMatchTime() { return matchTime; }
        public String getCurrentTime() { return currentTime; }
        public String getPeriod() { return period; }
        public Score getScore() { return score; }
        public OddsInfo getOdds() { return odds; }
        public String getHomeTeamEn() { return homeTeamEn; }
        public String getAwayTeamEn() { return awayTeamEn; }
        
        public void setCurrentTime(String currentTime) { this.currentTime = currentTime; }
        public void setPeriod(String period) { this.period = period; }
        public void setScore(Score score) { this.score = score; }
        public void setOdds(OddsInfo odds) { this.odds = odds; }
        public void setHomeTeamEn(String homeTeamEn) { this.homeTeamEn = homeTeamEn; }
        public void setAwayTeamEn(String awayTeamEn) { this.awayTeamEn = awayTeamEn; }
        
        @Override
        public String toString() {
            String statusText = getMatchStatusText(matchStatus);
            String timeInfo = currentTime != null ? currentTime + " " + period : "";
            
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("🏆 %s vs %s\n", homeTeam, awayTeam));
            sb.append(String.format("   状态: %s %s\n", statusText, timeInfo));
            
            if (score != null) {
                sb.append(String.format("   比分: %s\n", score.toString()));
            }
            
            return sb.toString();
        }
        
        private String getMatchStatusText(int status) {
            switch (status) {
                case 1: return "未开始";
                case 2: return "上半场";
                case 3: return "中场休息";
                case 4: return "下半场";
                case 5: return "全场结束";
                case 7: return "比赛中";
                default: return "未知状态(" + status + ")";
            }
        }
    }
}
