package com.zqsport.http;

/**
 * Configuration class for HTTP request parameters
 */
public class HttpConfig {
    
    // HTTP API URL components (基于真实请求)
    public static final String BASE_API_URL = "https://www.ps3838.com";
    public static final String ODDS_ENDPOINT = "/sports-service/sv/compact/events";
    public static final String ORIGIN = "https://www.ps3838.com";
    public static final String REFERER = "https://www.ps3838.com/zh-cn/sports/soccer";
    
    // Default request parameters (same as WebSocket subscription params)
    public static final class RequestParams {
        public static final int SPORT_ID = 29; // sp parameter
        public static final int MARKET_TYPE = 1; // mk parameter
        public static final String BET_TYPE_GROUP = "1"; // btg parameter
        public static final int ODDS_TYPE = 2; // ot parameter
        public static final int ORDER = 1; // o parameter
        public static final int LIMIT = 100; // l parameter
        public static final int MATCH_EVENT = 0; // me parameter
        public static final int COMPETITION_LIMIT = 100; // cl parameter
        public static final String GROUP = "QQ=="; // g parameter
        public static final String LOCALE = "zh_CN"; // locale parameter
        public static final String PIMO = "0,1,8,39,2,3,6,7,4,5"; // pimo parameter
        public static final int PAGE_NUMBER = -1; // pn parameter
        public static final int PAGE_VERSION = 1; // pv parameter
        public static final int TIME_MODE = 0; // tm parameter
    }
    
    // User-Agent for browser simulation (基于真实请求)
    public static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0";

    // HTTP请求不需要Token API配置

    // HTTP client timeouts
    public static final int CONNECTION_TIMEOUT = 10000; // 10 seconds
    public static final int REQUEST_TIMEOUT = 30000; // 30 seconds

    // Polling configuration
    public static final int DEFAULT_POLLING_INTERVAL = 5000; // 5 seconds
    public static final int MIN_POLLING_INTERVAL = 1000; // 1 second
    public static final int MAX_POLLING_INTERVAL = 60000; // 60 seconds

    // 重试配置
    public static final int MAX_RETRIES = 3;
    public static final long BASE_RETRY_DELAY_MS = 1000; // 1秒

    // 连接池配置
    public static final int MAX_TOTAL_CONNECTIONS = 20;
    public static final int MAX_CONNECTIONS_PER_ROUTE = 10;

    // 健康检查配置
    public static final String HEALTH_CHECK_URL = "https://www.google.com";
    public static final int HEALTH_CHECK_TIMEOUT = 5000; // 5秒
    
    /**
     * Build complete API URL for odds endpoint
     */
    public static String buildOddsApiUrl() {
        return BASE_API_URL + ODDS_ENDPOINT;
    }
    
    /**
     * Build query parameters string (基于真实请求格式)
     */
    public static String buildQueryParams(int sportId, String locale) {
        StringBuilder params = new StringBuilder();
        params.append("btg=").append(RequestParams.BET_TYPE_GROUP);
        params.append("&c=");
        params.append("&cl=").append(RequestParams.COMPETITION_LIMIT);
        params.append("&d=");
        params.append("&ec=");
        params.append("&ev=");
        params.append("&g=").append(java.net.URLEncoder.encode(RequestParams.GROUP, java.nio.charset.StandardCharsets.UTF_8));
        params.append("&hle=false");
        params.append("&ic=false");
        params.append("&ice=false");
        params.append("&inl=false");
        params.append("&l=").append(RequestParams.LIMIT);
        params.append("&lang=");
        params.append("&lg=");
        params.append("&lv=");
        params.append("&me=").append(RequestParams.MATCH_EVENT);
        params.append("&mk=").append(RequestParams.MARKET_TYPE);
        params.append("&more=false");
        params.append("&o=").append(RequestParams.ORDER);
        params.append("&ot=").append(RequestParams.ODDS_TYPE);
        params.append("&pa=0");
        params.append("&pimo=").append(java.net.URLEncoder.encode(RequestParams.PIMO, java.nio.charset.StandardCharsets.UTF_8));
        params.append("&pn=").append(RequestParams.PAGE_NUMBER);
        params.append("&pv=").append(RequestParams.PAGE_VERSION);
        params.append("&sp=").append(sportId);
        params.append("&tm=").append(RequestParams.TIME_MODE);
        params.append("&v=0");
        params.append("&locale=").append(locale);
        params.append("&_=").append(System.currentTimeMillis());
        params.append("&withCredentials=true");
        
        return params.toString();
    }
    
    /**
     * Build query parameters string with custom parameters
     */
    public static String buildQueryParams(int sportId, String locale, String token, String ulp) {
        String baseParams = buildQueryParams(sportId, locale);
        StringBuilder params = new StringBuilder(baseParams);
        
        if (token != null && !token.isEmpty()) {
            params.append("&token=").append(java.net.URLEncoder.encode(token, java.nio.charset.StandardCharsets.UTF_8));
        }
        if (ulp != null && !ulp.isEmpty()) {
            params.append("&ulp=").append(java.net.URLEncoder.encode(ulp, java.nio.charset.StandardCharsets.UTF_8));
        }
        
        return params.toString();
    }
}
