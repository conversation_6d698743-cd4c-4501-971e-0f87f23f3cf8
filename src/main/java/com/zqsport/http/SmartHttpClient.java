package com.zqsport.http;

import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.hc.core5.util.Timeout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.util.Random;

/**
 * 智能HTTP客户端 - 专门处理目标网站访问限制
 * 包含多种绕过策略和智能重试机制
 */
public class SmartHttpClient {
    
    private static final Logger logger = LoggerFactory.getLogger(SmartHttpClient.class);
    
    private CloseableHttpClient httpClient;
    private Random random = new Random();
    
    /**
     * 初始化智能HTTP客户端
     */
    public void initialize() {
        try {
            logger.info("🧠 初始化智能HTTP客户端...");
            
            // 创建宽松的SSL上下文
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(TrustAllStrategy.INSTANCE)
                    .build();
            
            SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                    sslContext, 
                    NoopHostnameVerifier.INSTANCE
            );
            
            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder
                    .<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", sslSocketFactory)
                    .build();
            
            PoolingHttpClientConnectionManager connectionManager = 
                    new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            connectionManager.setMaxTotal(5);  // 减少连接数，避免被检测
            connectionManager.setDefaultMaxPerRoute(2);
            
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(Timeout.ofMilliseconds(15000))  // 增加超时时间
                    .setResponseTimeout(Timeout.ofMilliseconds(30000))
                    .setConnectionRequestTimeout(Timeout.ofMilliseconds(10000))
                    .build();
            
            this.httpClient = HttpClients.custom()
                    .setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(requestConfig)
                    .build();
            
            logger.info("✅ 智能HTTP客户端初始化成功");
            
        } catch (Exception e) {
            logger.error("❌ 智能HTTP客户端初始化失败", e);
            this.httpClient = HttpClients.createDefault();
        }
    }
    
    /**
     * 智能执行GET请求 - 包含多种策略
     */
    public String executeSmartGet(String url) {
        if (httpClient == null) {
            initialize();
        }
        
        logger.info("🎯 智能请求: {}", url);
        
        // 策略1: 标准浏览器模拟
        String result = tryBrowserSimulation(url);
        if (result != null) {
            return result;
        }
        
        // 策略2: 移动端模拟
        result = tryMobileSimulation(url);
        if (result != null) {
            return result;
        }
        
        // 策略3: 简化请求
        result = trySimpleRequest(url);
        if (result != null) {
            return result;
        }
        
        // 策略4: 直接IP访问
        result = tryDirectIPAccess(url);
        if (result != null) {
            return result;
        }
        
        logger.error("❌ 所有策略都失败了");
        return createErrorResponse("ALL_STRATEGIES_FAILED", "所有访问策略都失败");
    }
    
    /**
     * 策略1: 标准浏览器模拟
     */
    private String tryBrowserSimulation(String url) {
        logger.info("🌐 尝试策略1: 标准浏览器模拟");
        
        try {
            HttpGet request = new HttpGet(url);
            setupBrowserHeaders(request);
            
            // 添加随机延迟，模拟人类行为
            Thread.sleep(1000 + random.nextInt(2000));
            
            return executeRequest(request, "浏览器模拟");
            
        } catch (Exception e) {
            logger.warn("策略1失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 策略2: 移动端模拟
     */
    private String tryMobileSimulation(String url) {
        logger.info("📱 尝试策略2: 移动端模拟");
        
        try {
            HttpGet request = new HttpGet(url);
            setupMobileHeaders(request);
            
            Thread.sleep(500 + random.nextInt(1000));
            
            return executeRequest(request, "移动端模拟");
            
        } catch (Exception e) {
            logger.warn("策略2失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 策略3: 简化请求
     */
    private String trySimpleRequest(String url) {
        logger.info("⚡ 尝试策略3: 简化请求");
        
        try {
            HttpGet request = new HttpGet(url);
            setupSimpleHeaders(request);
            
            return executeRequest(request, "简化请求");
            
        } catch (Exception e) {
            logger.warn("策略3失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 策略4: 直接IP访问
     */
    private String tryDirectIPAccess(String url) {
        logger.info("🔗 尝试策略4: 直接IP访问");
        
        try {
            // 将域名替换为IP地址
            String ipUrl = url.replace("www.ps3838.com", HttpConfig.TARGET_SERVER_IP);
            
            HttpGet request = new HttpGet(ipUrl);
            setupBrowserHeaders(request);
            // 保持Host头为原域名
            request.setHeader("Host", "www.ps3838.com");
            
            return executeRequest(request, "直接IP访问");
            
        } catch (Exception e) {
            logger.warn("策略4失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 设置浏览器头部
     */
    private void setupBrowserHeaders(HttpGet request) {
        request.setHeader("User-Agent", HttpConfig.USER_AGENT);
        request.setHeader("Accept", "application/json, text/plain, */*");
        request.setHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        request.setHeader("Accept-Encoding", "gzip, deflate, br");
        request.setHeader("Connection", "keep-alive");
        request.setHeader("Cache-Control", "no-cache");
        request.setHeader("Pragma", "no-cache");
        request.setHeader("Sec-Fetch-Dest", "empty");
        request.setHeader("Sec-Fetch-Mode", "cors");
        request.setHeader("Sec-Fetch-Site", "same-origin");
        
        try {
            if (request.getUri().toString().contains("ps3838.com")) {
                request.setHeader("Origin", HttpConfig.ORIGIN);
                request.setHeader("Referer", HttpConfig.REFERER);
            }
        } catch (Exception e) {
            logger.debug("设置特殊头部时出错: {}", e.getMessage());
        }
    }
    
    /**
     * 设置移动端头部
     */
    private void setupMobileHeaders(HttpGet request) {
        request.setHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1");
        request.setHeader("Accept", "application/json, text/plain, */*");
        request.setHeader("Accept-Language", "zh-CN,zh;q=0.9");
        request.setHeader("Accept-Encoding", "gzip, deflate, br");
        request.setHeader("Connection", "keep-alive");
    }
    
    /**
     * 设置简化头部
     */
    private void setupSimpleHeaders(HttpGet request) {
        request.setHeader("User-Agent", "curl/7.68.0");
        request.setHeader("Accept", "*/*");
    }
    
    /**
     * 执行请求
     */
    private String executeRequest(HttpGet request, String strategy) {
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getCode();
            String responseBody = EntityUtils.toString(response.getEntity());
            
            logger.info("📊 {} - 状态码: {}, 响应长度: {}", strategy, statusCode, responseBody.length());
            
            if (statusCode >= 200 && statusCode < 300) {
                logger.info("✅ {} 成功!", strategy);
                return responseBody;
            } else if (statusCode == 403) {
                logger.warn("⚠️ {} - 访问被禁止 (403)", strategy);
            } else if (statusCode == 429) {
                logger.warn("⚠️ {} - 请求过于频繁 (429)", strategy);
            } else {
                logger.warn("⚠️ {} - 状态码: {}", strategy, statusCode);
            }
            
            return null;
            
        } catch (java.net.ConnectException e) {
            logger.warn("❌ {} - 连接被拒绝", strategy);
            return null;
        } catch (Exception e) {
            logger.warn("❌ {} - 异常: {}", strategy, e.getMessage());
            return null;
        }
    }
    
    /**
     * 创建错误响应
     */
    private String createErrorResponse(String errorCode, String errorMessage) {
        return String.format("{\"error\": \"%s\", \"message\": \"%s\", \"timestamp\": %d}", 
                errorCode, errorMessage, System.currentTimeMillis());
    }
    
    /**
     * 关闭客户端
     */
    public void close() {
        if (httpClient != null) {
            try {
                httpClient.close();
                logger.info("🔒 智能HTTP客户端已关闭");
            } catch (Exception e) {
                logger.warn("关闭客户端时出错", e);
            }
        }
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        SmartHttpClient client = new SmartHttpClient();
        
        // 测试目标API
        String apiUrl = HttpConfig.buildOddsApiUrl() + "?" + 
                HttpConfig.buildQueryParams(29, "zh_CN");
        
        logger.info("🧪 开始智能访问测试...");
        String result = client.executeSmartGet(apiUrl);
        
        if (result != null && !result.contains("error")) {
            logger.info("🎉 访问成功! 响应长度: {} 字符", result.length());
            logger.info("📄 响应预览: {}", result.substring(0, Math.min(200, result.length())));
        } else {
            logger.error("💔 访问失败");
        }
        
        client.close();
    }
}
