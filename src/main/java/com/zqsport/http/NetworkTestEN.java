package com.zqsport.http;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.UnknownHostException;
import java.net.ConnectException;
import javax.net.ssl.SSLException;

/**
 * Network Connection Test (English Version)
 * Avoids Chinese character encoding issues
 */
public class NetworkTestEN {
    
    public static void main(String[] args) {
        System.out.println("=== Sports Data API Network Connection Test ===");
        System.out.println();
        
        // Test URLs
        String[] testUrls = {
            "https://www.baidu.com",
            "https://httpbin.org/get",
            "https://www.ps3838.com", 
            "https://www.ps3838.com/sports-service/sv/compact/events"
        };
        
        boolean anySuccess = false;
        
        for (String urlStr : testUrls) {
            System.out.println("Testing URL: " + urlStr);
            boolean success = testConnection(urlStr);
            anySuccess = anySuccess || success;
            System.out.println();
        }
        
        System.out.println("=".repeat(60));
        if (anySuccess) {
            System.out.println("SUCCESS: Network connection is working!");
            System.out.println("If sports API fails, you may need:");
            System.out.println("  1. Authentication tokens/cookies");
            System.out.println("  2. Complete browser header simulation");
            System.out.println("  3. SSL certificate handling");
        } else {
            System.out.println("FAILED: All connections failed!");
            System.out.println("Please check:");
            System.out.println("  1. Internet connection"); 
            System.out.println("  2. Firewall/proxy settings");
            System.out.println("  3. DNS resolution");
        }
        System.out.println("=".repeat(60));
    }
    
    private static boolean testConnection(String urlStr) {
        try {
            URL url = new URL(urlStr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // Set request properties
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10s connect timeout
            connection.setReadTimeout(15000);    // 15s read timeout
            
            // Set browser headers
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            connection.setRequestProperty("Accept", "application/json, text/plain, */*");
            connection.setRequestProperty("Accept-Language", "en-US,en;q=0.9");
            connection.setRequestProperty("Cache-Control", "no-cache");
            
            System.out.print("  Connecting... ");
            
            // Establish connection
            connection.connect();
            
            int responseCode = connection.getResponseCode();
            System.out.print("Status: " + responseCode + " ");
            
            if (responseCode >= 200 && responseCode < 300) {
                System.out.println("SUCCESS");
                
                // Read response
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                int lineCount = 0;
                
                while ((line = reader.readLine()) != null && lineCount < 5) {
                    response.append(line).append("\n");
                    lineCount++;
                }
                reader.close();
                
                String responseStr = response.toString();
                System.out.println("  Response length: " + responseStr.length() + " chars");
                
                if (responseStr.contains("{") || responseStr.contains("[")) {
                    System.out.println("  [JSON] Response contains JSON data");
                } else if (responseStr.toLowerCase().contains("html")) {
                    System.out.println("  [HTML] Response is HTML page");
                }
                
                // Show preview
                if (responseStr.length() > 100) {
                    System.out.println("  Preview: " + responseStr.substring(0, 100).replace("\n", " ") + "...");
                }
                
                return true;
                
            } else if (responseCode >= 300 && responseCode < 400) {
                System.out.println("REDIRECT");
                String location = connection.getHeaderField("Location");
                if (location != null) {
                    System.out.println("  Redirect to: " + location);
                }
                return true; // Redirect is also success
                
            } else {
                System.out.println("ERROR");
                
                // Try to read error response
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                    StringBuilder errorResponse = new StringBuilder();
                    String line;
                    int lineCount = 0;
                    while ((line = errorReader.readLine()) != null && lineCount < 3) {
                        errorResponse.append(line).append(" ");
                        lineCount++;
                    }
                    if (errorResponse.length() > 0) {
                        System.out.println("  Error: " + errorResponse.toString().substring(0, Math.min(150, errorResponse.length())));
                    }
                } catch (Exception e) {
                    // Ignore error reading exception
                }
                
                return false;
            }
            
        } catch (UnknownHostException e) {
            System.out.println("FAILED - Unknown Host");
            System.out.println("  Please check DNS settings or network connection");
            return false;
            
        } catch (ConnectException e) {
            System.out.println("FAILED - Connection Refused");
            System.out.println("  Please check firewall settings or target server status");
            return false;
            
        } catch (SSLException e) {
            System.out.println("FAILED - SSL Handshake Failed");
            System.out.println("  SSL certificate issue, may need trust certificate or use HTTP");
            return false;
            
        } catch (IOException e) {
            System.out.println("FAILED - IO Exception: " + e.getMessage());
            
            if (e.getMessage().contains("timeout")) {
                System.out.println("  Connection timeout, network may be slow");
            } else if (e.getMessage().contains("reset")) {
                System.out.println("  Connection reset, may be blocked by firewall");
            }
            
            return false;
            
        } catch (Exception e) {
            System.out.println("FAILED - Other Error: " + e.getMessage());
            return false;
        }
    }
}
