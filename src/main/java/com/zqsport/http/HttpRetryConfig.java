package com.zqsport.http;

/**
 * HTTP重试和连接配置类
 */
public class HttpRetryConfig {
    
    // 重试配置
    public static final int DEFAULT_MAX_RETRIES = 3;
    public static final long DEFAULT_BASE_DELAY_MS = 1000; // 1秒
    public static final long DEFAULT_MAX_DELAY_MS = 30000; // 30秒
    public static final double DEFAULT_BACKOFF_MULTIPLIER = 2.0; // 指数退避倍数
    
    // 连接池配置
    public static final int DEFAULT_MAX_TOTAL_CONNECTIONS = 20;
    public static final int DEFAULT_MAX_CONNECTIONS_PER_ROUTE = 10;
    public static final int DEFAULT_CONNECTION_VALIDATE_AFTER_INACTIVITY_MS = 30000; // 30秒
    
    // 超时配置（毫秒）
    public static final int DEFAULT_CONNECT_TIMEOUT_MS = 10000; // 10秒
    public static final int DEFAULT_SOCKET_TIMEOUT_MS = 30000; // 30秒
    public static final int DEFAULT_CONNECTION_REQUEST_TIMEOUT_MS = 10000; // 10秒
    
    // 失败处理配置
    public static final int CONSECUTIVE_FAILURES_WARNING_THRESHOLD = 5;
    public static final int CONSECUTIVE_FAILURES_PAUSE_THRESHOLD = 20;
    public static final long PAUSE_DURATION_SECONDS = 60;
    
    // 统计信息配置
    public static final int STATISTICS_PRINT_INTERVAL = 100; // 每100次成功请求打印统计
    
    /**
     * 计算重试延迟时间（指数退避）
     */
    public static long calculateRetryDelay(int attemptNumber, long baseDelayMs, double backoffMultiplier, long maxDelayMs) {
        if (attemptNumber <= 0) {
            return baseDelayMs;
        }
        
        long delay = (long) (baseDelayMs * Math.pow(backoffMultiplier, attemptNumber - 1));
        return Math.min(delay, maxDelayMs);
    }
    
    /**
     * 判断异常是否可以重试
     */
    public static boolean isRetryableException(Exception e) {
        // 网络连接相关异常可以重试
        if (e instanceof java.net.SocketException ||
            e instanceof java.net.SocketTimeoutException ||
            e instanceof java.net.ConnectException ||
            e instanceof java.net.UnknownHostException) {
            return true;
        }
        
        // IO异常可以重试
        if (e instanceof java.io.IOException) {
            String message = e.getMessage();
            if (message != null) {
                message = message.toLowerCase();
                // 特定的IO异常消息可以重试
                return message.contains("connection reset") ||
                       message.contains("connection refused") ||
                       message.contains("read timed out") ||
                       message.contains("connect timed out") ||
                       message.contains("broken pipe");
            }
            return true;
        }
        
        return false;
    }
    
    /**
     * 判断HTTP状态码是否可以重试
     */
    public static boolean isRetryableHttpStatus(int statusCode) {
        // 5xx 服务器错误可以重试
        if (statusCode >= 500 && statusCode < 600) {
            return true;
        }
        
        // 429 请求过于频繁可以重试（但需要更长延迟）
        if (statusCode == 429) {
            return true;
        }
        
        // 408 请求超时可以重试
        if (statusCode == 408) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取HTTP状态码对应的重试延迟倍数
     */
    public static int getRetryDelayMultiplier(int statusCode) {
        switch (statusCode) {
            case 429: // Too Many Requests - 需要更长延迟
                return 5;
            case 503: // Service Unavailable - 需要较长延迟
                return 3;
            case 502: // Bad Gateway - 正常延迟
            case 504: // Gateway Timeout - 正常延迟
            default:
                return 1;
        }
    }
}
