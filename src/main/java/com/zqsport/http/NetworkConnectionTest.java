package com.zqsport.http;

import com.zqsport.util.NetworkDiagnostics;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 网络连接测试程序
 * 专门用于诊断体育数据API的连接问题
 */
public class NetworkConnectionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(NetworkConnectionTest.class);
    
    public static void main(String[] args) {
        logger.info("🌐 开始体育数据API网络连接测试...");
        
        // 测试目标URL
        String baseUrl = "https://www.ps3838.com";
        String apiUrl = "https://www.ps3838.com/sports-service/sv/compact/events";
        String fullApiUrl = apiUrl + "?" + HttpConfig.buildQueryParams(29, "zh_CN");
        
        logger.info("=".repeat(80));
        logger.info("🎯 测试目标:");
        logger.info("基础URL: {}", baseUrl);
        logger.info("API URL: {}", apiUrl);
        logger.info("完整API URL: {}", fullApiUrl);
        logger.info("=".repeat(80));
        
        boolean overallSuccess = false;
        
        // 测试1：基础连接
        logger.info("\n🔗 测试1: 基础网站连接");
        boolean baseSuccess = NetworkDiagnostics.diagnoseConnection(baseUrl);
        overallSuccess = baseSuccess || overallSuccess;
        
        // 测试2：API端点连接
        logger.info("\n🔗 测试2: API端点连接");
        boolean apiSuccess = NetworkDiagnostics.diagnoseConnection(apiUrl);
        overallSuccess = apiSuccess || overallSuccess;
        
        // 测试3：完整API调用
        logger.info("\n🔗 测试3: 完整API调用（带参数）");
        boolean fullApiSuccess = NetworkDiagnostics.diagnoseConnection(fullApiUrl);
        overallSuccess = fullApiSuccess || overallSuccess;
        
        // 测试4：使用增强的HTTP客户端
        logger.info("\n🔗 测试4: 使用增强HTTP客户端");
        boolean enhancedSuccess = testWithEnhancedClient();
        overallSuccess = enhancedSuccess || overallSuccess;
        
        // 总结
        logger.info("\n" + "=".repeat(80));
        logger.info("🏁 测试总结:");
        logger.info("基础网站连接: {}", baseSuccess ? "✅ 成功" : "❌ 失败");
        logger.info("API端点连接: {}", apiSuccess ? "✅ 成功" : "❌ 失败");
        logger.info("完整API调用: {}", fullApiSuccess ? "✅ 成功" : "❌ 失败");
        logger.info("增强HTTP客户端: {}", enhancedSuccess ? "✅ 成功" : "❌ 失败");
        logger.info("总体结果: {}", overallSuccess ? "✅ 至少一项成功" : "❌ 全部失败");
        
        if (!overallSuccess) {
            logger.error("\n💡 建议解决方案:");
            logger.error("1. 检查网络连接和防火墙设置");
            logger.error("2. 尝试使用VPN或代理");
            logger.error("3. 检查目标网站是否可访问");
            logger.error("4. 验证API端点是否正确");
            logger.error("5. 检查是否需要特殊的认证信息");
        } else {
            logger.info("\n🎉 网络连接正常，可以继续使用程序！");
        }
        
        logger.info("=".repeat(80));
    }
    
    /**
     * 测试增强的HTTP客户端
     */
    private static boolean testWithEnhancedClient() {
        try {
            SportsHttpClient client = new SportsHttpClient();
            
            logger.info("🧪 使用SportsHttpClient进行连接测试...");
            
            // 设置测试认证参数（可选）
            client.setCredentials("test_token", "test_ulp");
            
            // 尝试获取数据
            String response = client.getOddsData(29, "zh_CN");
            
            if (response != null && !response.isEmpty()) {
                logger.info("✅ 增强HTTP客户端测试成功");
                logger.info("响应长度: {} 字符", response.length());
                
                if (response.contains("{") || response.contains("[")) {
                    logger.info("✅ 响应包含JSON数据");
                } else {
                    logger.warn("⚠️ 响应不是JSON格式，可能是错误页面");
                }
                
                client.close();
                return true;
            } else {
                logger.warn("⚠️ 增强HTTP客户端返回空响应");
                client.close();
                return false;
            }
            
        } catch (Exception e) {
            logger.error("❌ 增强HTTP客户端测试失败: {}", e.getMessage());
            logger.debug("详细错误信息:", e);
            return false;
        }
    }
    
    /**
     * 显示网络配置信息
     */
    private static void showNetworkConfig() {
        logger.info("\n🔧 当前网络配置:");
        logger.info("Java版本: {}", System.getProperty("java.version"));
        logger.info("操作系统: {}", System.getProperty("os.name"));
        logger.info("默认连接超时: {}ms", HttpConfig.CONNECTION_TIMEOUT);
        logger.info("默认请求超时: {}ms", HttpConfig.REQUEST_TIMEOUT);
        logger.info("User-Agent: {}", HttpConfig.USER_AGENT);
        
        // 显示系统属性
        String[] networkProps = {
            "java.net.useSystemProxies",
            "https.proxyHost",
            "https.proxyPort",
            "http.proxyHost", 
            "http.proxyPort"
        };
        
        for (String prop : networkProps) {
            String value = System.getProperty(prop);
            if (value != null) {
                logger.info("{}: {}", prop, value);
            }
        }
    }
}
