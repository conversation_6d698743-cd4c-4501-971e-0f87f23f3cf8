package com.zqsport;

import com.zqsport.http.SportsHttpManager;
import com.zqsport.http.HttpConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Scanner;

/**
 * Main application for connecting to sports data via HTTP polling
 */
public class SportsDataHttpApp {
    
    private static final Logger logger = LoggerFactory.getLogger(SportsDataHttpApp.class);
    
    public static void main(String[] args) {
        SportsHttpManager manager = new SportsHttpManager();
        
        try {
            logger.info("启动体育数据HTTP应用...");
            
            // 直接连接（不需要token认证）
            boolean connected = manager.connect();
            
            if (!connected) {
                logger.error("连接失败，程序退出...");
                return;
            }
            
            // 订阅赔率数据（开始轮询）
            logger.info("开始订阅赔率数据...");
            manager.subscribeToOdds();
            
            // 保持应用运行并允许用户交互
            try (Scanner scanner = new Scanner(System.in)) {
                boolean running = true;
                
                logger.info("应用正在运行。输入 'help' 查看命令或 'quit' 退出。");
                
                while (running) {
                System.out.print("请输入命令: ");
                String command = scanner.nextLine().trim().toLowerCase();
                
                switch (command) {
                    case "help":
                        printHelp();
                        break;
                        
                    case "status":
                        System.out.println("HTTP轮询状态: " + (manager.isPolling() ? "运行中" : "已停止"));
                        manager.printStatistics();
                        break;
                        
                    case "start":
                        if (manager.isPolling()) {
                            System.out.println("轮询已在运行中");
                        } else {
                            boolean started = manager.startPolling();
                            System.out.println("启动轮询: " + (started ? "成功" : "失败"));
                        }
                        break;
                        
                    case "stop":
                        manager.stopPolling();
                        System.out.println("轮询已停止");
                        break;
                        
                    case "subscribe":
                        System.out.print("输入体育ID (默认 29): ");
                        String sportIdInput = scanner.nextLine().trim();
                        int sportId = sportIdInput.isEmpty() ? 29 : Integer.parseInt(sportIdInput);
                        
                        System.out.print("输入语言区域 (默认 zh_CN): ");
                        String locale = scanner.nextLine().trim();
                        if (locale.isEmpty()) locale = "zh_CN";
                        
                        manager.subscribeToOdds(sportId, locale);
                        System.out.println("订阅参数已设置: 体育ID=" + sportId + ", 语言=" + locale);
                        break;
                        
                    case "interval":
                        System.out.print("输入轮询间隔(毫秒，" + HttpConfig.MIN_POLLING_INTERVAL 
                                + "-" + HttpConfig.MAX_POLLING_INTERVAL + "): ");
                        try {
                            String intervalInput = scanner.nextLine().trim();
                            if (!intervalInput.isEmpty()) {
                                int interval = Integer.parseInt(intervalInput);
                                manager.setPollingInterval(interval);
                                System.out.println("轮询间隔已设置为: " + interval + "ms");
                                
                                // 如果正在运行，重启轮询以应用新间隔
                                if (manager.isPolling()) {
                                    System.out.println("重启轮询以应用新间隔...");
                                    manager.stopPolling();
                                    Thread.sleep(1000);
                                    manager.startPolling();
                                }
                            }
                        } catch (NumberFormatException e) {
                            System.out.println("无效的间隔时间格式");
                        }
                        break;
                        
                    case "fetch":
                        System.out.println("手动获取赔率数据...");
                        String response = manager.fetchOddsNow();
                        if (response != null) {
                            System.out.println("获取成功，数据长度: " + response.length());
                        } else {
                            System.out.println("获取失败");
                        }
                        break;
                        
                    case "stats":
                        manager.printStatistics();
                        break;
                        
                    case "reset":
                        manager.resetStatistics();
                        System.out.println("统计信息已重置");
                        break;
                        
                    case "reconnect":
                        logger.info("重新连接...");
                        manager.disconnect();
                        Thread.sleep(2000);
                        connected = manager.connect();
                        if (connected) {
                            manager.subscribeToOdds();
                            System.out.println("重新连接成功");
                        } else {
                            System.out.println("重新连接失败");
                        }
                        break;
                        
                    case "quit":
                    case "exit":
                        running = false;
                        break;
                        
                    default:
                        System.out.println("未知命令: " + command + "。输入 'help' 查看可用命令。");
                }
            }
            } // 关闭scanner的try-with-resources
            
        } catch (Exception e) {
            logger.error("主应用程序出错", e);
        } finally {
            // 清理资源
            logger.info("正在关闭应用程序...");
            manager.disconnect();
        }
    }
    
    private static void printHelp() {
        System.out.println("\n可用命令:");
        System.out.println("  help       - 显示此帮助信息");
        System.out.println("  status     - 显示HTTP轮询状态和统计信息");
        System.out.println("  start      - 开始轮询");
        System.out.println("  stop       - 停止轮询");
        System.out.println("  subscribe  - 设置订阅参数（体育ID和语言）");
        System.out.println("  interval   - 设置轮询间隔");
        System.out.println("  fetch      - 手动获取一次赔率数据");
        System.out.println("  stats      - 显示详细统计信息");
        System.out.println("  reset      - 重置统计信息");
        System.out.println("  reconnect  - 重新连接");
        System.out.println("  quit/exit  - 退出应用程序");
        System.out.println();
        System.out.println("HTTP轮询说明:");
        System.out.println("  - 默认轮询间隔: " + HttpConfig.DEFAULT_POLLING_INTERVAL + "ms");
        System.out.println("  - 最小轮询间隔: " + HttpConfig.MIN_POLLING_INTERVAL + "ms");
        System.out.println("  - 最大轮询间隔: " + HttpConfig.MAX_POLLING_INTERVAL + "ms");
        System.out.println("  - 默认体育ID: " + HttpConfig.RequestParams.SPORT_ID);
        System.out.println("  - 默认语言: " + HttpConfig.RequestParams.LOCALE);
        System.out.println();
    }
}

