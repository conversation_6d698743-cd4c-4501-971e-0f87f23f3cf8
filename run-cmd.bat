@echo off
:: 强制使用UTF-8编码
chcp 65001

echo.
echo =======================================
echo   体育数据HTTP客户端 - 中文编码测试
echo =======================================
echo.

echo 当前代码页: 65001 (UTF-8)
echo.

echo 测试中文输出：
echo 🌐 网络连接测试
echo ✅ 连接成功  
echo ❌ 连接失败
echo ⚠️ 警告信息
echo 🔍 正在检查网络...
echo 📍 目标API: https://www.ps3838.com
echo 💡 建议: 检查防火墙设置
echo 🎉 测试完成！
echo.

echo 正在启动控制台编码测试程序...
echo.

java "-Dfile.encoding=UTF-8" "-Dconsole.encoding=UTF-8" -cp target/classes com.zqsport.util.ConsoleTest

echo.
echo 如果上面的中文显示正常，请使用以下命令启动主程序:
echo java "-Dfile.encoding=UTF-8" "-Dconsole.encoding=UTF-8" -cp "target/classes;target/dependency/*" com.zqsport.SportsDataHttpApp
echo.
pause
