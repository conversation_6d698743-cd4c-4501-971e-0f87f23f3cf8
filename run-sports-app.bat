@echo off
:: 设置控制台为UTF-8编码
chcp 65001 >nul 2>&1

:: 设置窗口标题
title 体育数据HTTP客户端

echo.
echo ========================================
echo    体育数据HTTP客户端启动
echo ========================================
echo.

:: 检查Java是否可用
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：找不到Java运行环境
    echo 请确保已安装Java并配置了PATH环境变量
    pause
    exit /b 1
)

:: 检查编译是否完成
if not exist "target\classes" (
    echo 🔧 正在编译项目...
    call mvn compile
    if %errorlevel% neq 0 (
        echo ❌ 编译失败，请检查代码
        pause
        exit /b 1
    )
)

echo 🚀 启动体育数据HTTP客户端...
echo.

:: 运行主程序，设置UTF-8编码
java ^
    "-Dfile.encoding=UTF-8" ^
    "-Dconsole.encoding=UTF-8" ^
    "-Djava.net.useSystemProxies=true" ^
    -cp "target/classes;target/dependency/*" ^
    com.zqsport.SportsDataHttpApp

echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
